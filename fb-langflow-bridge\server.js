// server.js - Application entry point
const { createApp, startServer } = require('./src/app');
const logger = require('./src/utils/logger');

/**
 * Main entry point for the application
 */
function main() {
  try {
    logger.info('🚀 Starting FB-Langflow Bridge Server...');
    
    // Create Express application
    const app = createApp();
    
    // Start server
    const server = startServer(app);
    
    // Log successful startup
    logger.success('✅ Application started successfully');
    
    return server;
    
  } catch (error) {
    logger.error('❌ Failed to start application:', {
      error: error.message,
      stack: error.stack
    });
    process.exit(1);
  }
}

// Start the application if this file is run directly
if (require.main === module) {
  main();
}

module.exports = { main };
