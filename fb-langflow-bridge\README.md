# FB-Langflow Bridge

A secure bridge server connecting Facebook Messenger to Langflow AI workflows with enhanced security features and production-ready deployment configuration.

## 🚀 Features

- **Secure Facebook Messenger Integration**: Handle webhooks with signature verification
- **Langflow AI Workflow Integration**: Trigger AI workflows and handle callbacks
- **Enhanced Security**: Rate limiting, input validation, authentication, CORS protection
- **Production Ready**: Docker containerization, health checks, graceful shutdown
- **Comprehensive Logging**: Structured logging with different levels
- **Caching System**: User profile caching with TTL and cleanup
- **Admin API**: System monitoring and management endpoints
- **FlightControl.dev Ready**: Pre-configured for easy deployment

## 📋 Table of Contents

- [Quick Start](#quick-start)
- [Installation](#installation)
- [Configuration](#configuration)
- [Deployment](#deployment)
- [API Documentation](#api-documentation)
- [Security](#security)
- [Development](#development)
- [Troubleshooting](#troubleshooting)

## 🚀 Quick Start

### Prerequisites

- Node.js 16+ 
- Facebook Developer Account
- Langflow Instance
- FlightControl.dev Account (for deployment)

### Local Development

1. **Clone and Install**
   ```bash
   git clone <your-repo>
   cd fb-langflow-bridge
   npm install
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start Development Server**
   ```bash
   npm run dev
   ```

4. **Test the Setup**
   ```bash
   curl http://localhost:3000/health
   ```

## 📦 Installation

### Local Installation

```bash
# Install dependencies
npm install

# Copy environment template
cp .env.example .env

# Edit configuration
nano .env

# Start the server
npm start
```

### Docker Installation

```bash
# Build the image
docker build -t fb-langflow-bridge .

# Run the container
docker run -p 3000:3000 --env-file .env fb-langflow-bridge
```

## ⚙️ Configuration

### Environment Variables

#### Required Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `VERIFY_TOKEN` | Facebook webhook verification token | `your_verify_token_123` |
| `FB_TOKEN` | Facebook Page Access Token | `EAAxxxxx...` |
| `LANGFLOW_API_URL` | Langflow webhook URL | `https://langflow.com/api/v1/webhook/flow-id` |
| `WEBHOOK_SECRET` | Webhook signature secret (32+ chars) | `your_webhook_secret_32_chars_min` |
| `ADMIN_API_KEY` | Admin API authentication key (32+ chars) | `your_admin_key_32_chars_minimum` |

#### Security Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `JWT_SECRET` | JWT token secret | Auto-generated in dev |
| `ENCRYPTION_KEY` | Data encryption key | Auto-generated in dev |
| `ALLOWED_ORIGINS` | CORS allowed origins | `http://localhost:3000` |
| `TRUSTED_IPS` | IP whitelist (optional) | Empty |

#### Optional Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NODE_ENV` | Environment mode | `development` |
| `PORT` | Server port | `3000` |
| `LOG_LEVEL` | Logging level | `info` |
| `DEBUG_MODE` | Enable debug features | `false` |
| `RATE_LIMIT_MAX` | Rate limit per window | `100` |
| `MAX_MESSAGE_LENGTH` | Max message length | `2000` |

### Facebook Setup

1. **Create Facebook App**
   - Go to [Facebook Developers](https://developers.facebook.com/)
   - Create new app → Business → App name

2. **Configure Messenger**
   - Add Messenger product
   - Generate Page Access Token
   - Set webhook URL: `https://your-domain.com/webhook`
   - Set verify token (same as `VERIFY_TOKEN` in .env)

3. **Subscribe to Events**
   - messages
   - messaging_postbacks
   - message_deliveries

### Langflow Setup

1. **Deploy Langflow Instance**
   - Use Langflow Cloud or self-hosted
   - Create your AI workflow
   - Get webhook URL from flow settings

2. **Configure Callback**
   - Set callback URL: `https://your-domain.com/langflow-callback`
   - Ensure flow returns results via callback

## 🚀 Deployment

### FlightControl.dev Deployment

1. **Prepare Repository**
   ```bash
   git add .
   git commit -m "Ready for deployment"
   git push origin main
   ```

2. **Configure FlightControl**
   - Connect your GitHub repository
   - The `flightcontrol.json` is pre-configured
   - Set environment secrets in FlightControl dashboard

3. **Required Secrets in FlightControl**
   ```
   VERIFY_TOKEN
   FB_TOKEN
   LANGFLOW_API_URL
   LANGFLOW_BASE_URL
   WEBHOOK_SECRET
   ADMIN_API_KEY
   JWT_SECRET
   ENCRYPTION_KEY
   BASE_URL
   ALLOWED_ORIGINS
   ```

4. **Deploy**
   - Push to `main` branch for production
   - Push to `develop` branch for staging

### Manual Docker Deployment

```bash
# Build production image
docker build -t fb-langflow-bridge:latest .

# Run with environment file
docker run -d \
  --name fb-langflow-bridge \
  -p 3000:3000 \
  --env-file .env.production \
  --restart unless-stopped \
  fb-langflow-bridge:latest
```

### Health Checks

The application provides health check endpoints:

- `GET /health` - Basic health check
- `GET /admin/status` - Detailed system status (requires admin auth)

## 📚 API Documentation

### Public Endpoints

#### Health Check
```http
GET /health
```

Response:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "2.0.0"
}
```

#### Webhook Verification
```http
GET /webhook?hub.mode=subscribe&hub.verify_token=TOKEN&hub.challenge=CHALLENGE
```

#### Webhook Events
```http
POST /webhook
Content-Type: application/json
X-Hub-Signature-256: sha256=signature

{
  "object": "page",
  "entry": [...]
}
```

### Admin Endpoints

All admin endpoints require `X-API-Key` header with admin API key.

#### System Status
```http
GET /admin/status
X-API-Key: your_admin_api_key
```

#### Test Profile Fetch
```http
GET /admin/test-profile/{userId}
X-API-Key: your_admin_api_key
```

#### Test Langflow
```http
POST /admin/test-langflow
X-API-Key: your_admin_api_key
Content-Type: application/json

{
  "message": "Test message",
  "userId": "test-user"
}
```

#### Cache Management
```http
GET /admin/cache
POST /admin/cache/clear
X-API-Key: your_admin_api_key
```

## 🔒 Security

### Security Features

- **Webhook Signature Verification**: Validates Facebook webhook signatures
- **Rate Limiting**: Prevents abuse with configurable limits
- **Input Validation**: Sanitizes and validates all inputs
- **CORS Protection**: Configurable cross-origin resource sharing
- **Security Headers**: Comprehensive security headers
- **Admin Authentication**: API key authentication for admin endpoints
- **IP Whitelisting**: Optional IP-based access control
- **Request Logging**: Comprehensive request logging for monitoring

### Security Best Practices

1. **Use Strong Secrets**
   ```bash
   # Generate secure secrets
   node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
   ```

2. **Enable Webhook Verification**
   ```env
   ENABLE_WEBHOOK_SIGNATURE_VERIFICATION=true
   ```

3. **Configure CORS Properly**
   ```env
   ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
   ```

4. **Use HTTPS in Production**
   ```env
   BASE_URL=https://yourdomain.com
   ```

5. **Regular Secret Rotation**
   - Rotate secrets every 90 days
   - Use different secrets for different environments

## 🛠️ Development

### Project Structure

```
fb-langflow-bridge/
├── src/
│   ├── config/           # Configuration management
│   ├── controllers/      # Route handlers
│   ├── middleware/       # Custom middleware
│   ├── services/         # Business logic services
│   ├── utils/           # Utility functions
│   └── app.js           # Main application setup
├── public/              # Static files
├── tests/               # Test files
├── deployment/          # Deployment configurations
├── docs/               # Documentation
├── server.js           # Entry point
├── package.json        # Dependencies
├── Dockerfile          # Container configuration
└── flightcontrol.json  # FlightControl deployment config
```

### Development Scripts

```bash
# Start development server with auto-reload
npm run dev

# Start production server
npm start

# Run tests
npm test

# Build Docker image
npm run docker:build

# Run Docker container
npm run docker:run
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Webhook Verification Failed

**Problem**: Facebook webhook verification fails

**Solutions**:
```bash
# Check verify token
echo $VERIFY_TOKEN

# Test webhook endpoint
curl "http://localhost:3000/webhook?hub.mode=subscribe&hub.verify_token=$VERIFY_TOKEN&hub.challenge=test"
```

#### 2. Langflow Connection Failed

**Problem**: Cannot connect to Langflow

**Solutions**:
```bash
# Test Langflow URL
curl -X POST $LANGFLOW_API_URL \
  -H "Content-Type: application/json" \
  -d '{"message": "test"}'
```

#### 3. High Memory Usage

**Problem**: Application consuming too much memory

**Solutions**:
```bash
# Check cache size
curl -H "X-API-Key: $ADMIN_API_KEY" http://localhost:3000/admin/cache

# Clear cache
curl -X POST -H "X-API-Key: $ADMIN_API_KEY" http://localhost:3000/admin/cache/clear
```

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Add tests
5. Submit pull request

---

**Built with ❤️ for secure, scalable Facebook Messenger integration**
