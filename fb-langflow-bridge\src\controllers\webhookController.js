// src/controllers/webhookController.js - Webhook handling controller
const config = require('../config');
const logger = require('../utils/logger');
const { ErrorHandler } = require('../utils/errorHandler');
const facebookService = require('../services/facebookService');
const langflowService = require('../services/langflowService');
const cacheService = require('../services/cacheService');

/**
 * Webhook controller for handling Facebook Messenger webhooks
 */
class WebhookController {
  /**
   * Verify webhook (GET /webhook)
   */
  static async verifyWebhook(req, res) {
    try {
      const { 'hub.mode': mode, 'hub.verify_token': token, 'hub.challenge': challenge } = req.query;

      try {
        const result = facebookService.verifyWebhook(mode, token, challenge);
        res.status(200).send(result);
      } catch (verifyError) {
        res.sendStatus(403);
      }
    } catch (error) {
      ErrorHandler.handleError(error, req, res);
    }
  }

  /**
   * Handle webhook events (POST /webhook)
   */
  static async handleWebhook(req, res) {
    try {
      // Log webhook request (limited to avoid spam)
      if (config.isDebugMode()) {
        logger.debug('Webhook request received', {
          ip: req.ip,
          headers: Object.keys(req.headers),
          bodySize: JSON.stringify(req.body).length
        });
      }

      // Process webhook events
      const events = facebookService.processWebhookEvent(req.body);
      
      if (events.length === 0) {
        return res.status(200).send('NO_EVENTS');
      }

      // Process each event asynchronously
      events.forEach(event => {
        if (event.type === 'message') {
          this.handleMessageEvent(event).catch(error => {
            logger.error('Error processing message event', {
              senderId: event.senderId,
              error: error.message
            });
          });
        } else if (event.type === 'postback') {
          this.handlePostbackEvent(event).catch(error => {
            logger.error('Error processing postback event', {
              senderId: event.senderId,
              error: error.message
            });
          });
        }
      });

      res.status(200).send('EVENT_RECEIVED');
      
    } catch (error) {
      logger.error('Webhook handling failed', {
        error: error.message,
        body: JSON.stringify(req.body).substring(0, 500)
      });
      res.status(200).send('ERROR_HANDLED'); // Always return 200 to Facebook
    }
  }

  /**
   * Handle message events
   */
  static async handleMessageEvent(event) {
    const { senderId, messageText, timestamp } = event;
    const logTimestamp = new Date(timestamp).toISOString().slice(11, 19);

    try {
      logger.webhook(event, { timestamp: logTimestamp });

      // Show typing indicator
      await facebookService.sendTypingIndicator(senderId, true);

      // Get user profile (with caching)
      let userProfile = cacheService.getUserProfile(senderId);
      
      if (!userProfile) {
        userProfile = await facebookService.getUserProfile(senderId);
        if (userProfile) {
          cacheService.setUserProfile(senderId, userProfile);
        }
      }

      const userName = userProfile?.first_name || 'User';
      
      // Trigger Langflow workflow
      const workflowResult = await langflowService.triggerWorkflow(
        messageText, 
        senderId, 
        userProfile
      );

      logger.info(`Langflow workflow triggered for ${userName}`, {
        senderId: senderId.substring(0, 8) + '...',
        resultType: workflowResult.type,
        status: workflowResult.status
      });

      // Handle immediate responses
      if (workflowResult.type === 'immediate' && workflowResult.result) {
        await facebookService.sendTypingIndicator(senderId, false);
        await facebookService.sendTextMessage(senderId, workflowResult.result);
      }

      // Cache the interaction
      cacheService.setUserSession(senderId, {
        lastMessage: messageText,
        lastResponse: workflowResult,
        timestamp: Date.now()
      });

    } catch (error) {
      logger.error('Message handling failed', {
        senderId: senderId.substring(0, 8) + '...',
        messageText: messageText.substring(0, 100),
        error: error.message
      });

      // Send error message to user
      try {
        await facebookService.sendTypingIndicator(senderId, false);
        await facebookService.sendTextMessage(senderId, 'Xin lỗi, có lỗi xảy ra. Vui lòng thử lại sau.');
      } catch (sendError) {
        logger.error('Failed to send error message', {
          senderId: senderId.substring(0, 8) + '...',
          error: sendError.message
        });
      }
    }
  }

  /**
   * Handle postback events
   */
  static async handlePostbackEvent(event) {
    const { senderId, payload, title, timestamp } = event;

    try {
      logger.info('Postback received', {
        senderId: senderId.substring(0, 8) + '...',
        payload,
        title
      });

      // Handle different postback payloads
      switch (payload) {
        case 'GET_STARTED':
          await this.handleGetStarted(senderId);
          break;
        case 'HELP':
          await this.handleHelp(senderId);
          break;
        default:
          // Treat unknown postbacks as regular messages
          await this.handleMessageEvent({
            senderId,
            messageText: title || payload,
            timestamp
          });
      }

    } catch (error) {
      logger.error('Postback handling failed', {
        senderId: senderId.substring(0, 8) + '...',
        payload,
        error: error.message
      });
    }
  }

  /**
   * Handle get started postback
   */
  static async handleGetStarted(senderId) {
    try {
      const welcomeMessage = 'Xin chào! Tôi là trợ lý AI. Bạn có thể hỏi tôi bất cứ điều gì.';
      await facebookService.sendTextMessage(senderId, welcomeMessage);
    } catch (error) {
      logger.error('Get started handling failed', {
        senderId: senderId.substring(0, 8) + '...',
        error: error.message
      });
    }
  }

  /**
   * Handle help postback
   */
  static async handleHelp(senderId) {
    try {
      const helpMessage = 'Hề lô';
      await facebookService.sendTextMessage(senderId, helpMessage);
    } catch (error) {
      logger.error('Help handling failed', {
        senderId: senderId.substring(0, 8) + '...',
        error: error.message
      });
    }
  }

  /**
   * Handle Langflow callback (POST /langflow-callback)
   */
  static async handleLangflowCallback(req, res) {
    try {
      const callbackResult = await langflowService.handleCallback(req.body);
      
      if (callbackResult.success && callbackResult.action === 'send_message') {
        // Send result to user
        await facebookService.sendTypingIndicator(callbackResult.sessionId, false);
        await facebookService.sendTextMessage(callbackResult.sessionId, callbackResult.result);
        
        logger.langflow('Callback result sent to user', {
          sessionId: callbackResult.sessionId.substring(0, 8) + '...'
        });
        
      } else if (callbackResult.action === 'send_error') {
        // Send error message to user
        await facebookService.sendTypingIndicator(callbackResult.sessionId, false);
        await facebookService.sendTextMessage(callbackResult.sessionId, 'Xin lỗi, có lỗi xảy ra khi xử lý yêu cầu của bạn.');
        
        logger.error('Langflow callback reported error', {
          sessionId: callbackResult.sessionId.substring(0, 8) + '...',
          error: callbackResult.error
        });
      }

      res.json({ success: true });

    } catch (error) {
      logger.error('Langflow callback handling failed', {
        body: JSON.stringify(req.body),
        error: error.message
      });

      // Try to send error message if session_id is available
      if (req.body.session_id) {
        try {
          await facebookService.sendTypingIndicator(req.body.session_id, false);
          await facebookService.sendTextMessage(req.body.session_id, 'Xin lỗi, có lỗi xảy ra.');
        } catch (sendError) {
          logger.error('Failed to send callback error message', {
            error: sendError.message
          });
        }
      }

      res.status(500).json({ success: false, error: 'Callback processing failed' });
    }
  }

  /**
   * Get webhook statistics
   */
  static getStats() {
    return {
      cacheStats: cacheService.getStats(),
      facebookHealth: 'connected', // Could add actual health check
      langflowHealth: 'connected'   // Could add actual health check
    };
  }
}

module.exports = WebhookController;
