// src/app.js - Main application setup
const express = require('express');
const helmet = require('helmet');
const cors = require('cors');
const path = require('path');

// Import configuration and utilities
const config = require('./config');
const logger = require('./utils/logger');
const { ErrorHandler } = require('./utils/errorHandler');

// Import middleware
const SecurityMiddleware = require('./middleware/security');

// Import controllers
const WebhookController = require('./controllers/webhookController');
const AdminController = require('./controllers/adminController');
const StaticController = require('./controllers/staticController');

/**
 * Create and configure Express application
 */
function createApp() {
  const app = express();

  // Initialize global error handlers
  ErrorHandler.initialize();

  // Trust proxy (important for deployment)
  app.set('trust proxy', 1);

  // Security middleware
  app.use(helmet({
    contentSecurityPolicy: false, // Disable CSP to avoid conflicts with static files
    crossOriginEmbedderPolicy: false
  }));

  // CORS configuration
  app.use(cors({
    origin: config.get('ALLOWED_ORIGINS'),
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key', 'X-Request-ID']
  }));

  // Request ID and security headers
  app.use(SecurityMiddleware.requestId());
  app.use(SecurityMiddleware.securityHeaders());

  // Body parsing with size limits and raw body capture
  app.use(express.json({ 
    limit: '10mb',
    verify: (req, res, buf) => {
      req.rawBody = buf;
    }
  }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // Body size limit check
  app.use(SecurityMiddleware.bodySizeLimit());

  // Request logging
  app.use(SecurityMiddleware.requestLogger());

  // Rate limiting
  app.use(SecurityMiddleware.rateLimiter());
  app.use(SecurityMiddleware.webhookRateLimiter());

  // IP whitelist (if enabled)
  app.use(SecurityMiddleware.ipWhitelist());

  // Static file serving with security
  app.use('/static', StaticController.setSecurityHeaders, express.static(path.join(__dirname, '../public')));
  app.use('/css', StaticController.setSecurityHeaders, express.static(path.join(__dirname, '../public/css')));

  // Routes setup
  setupRoutes(app);

  // Error handling middleware (must be last)
  app.use(ErrorHandler.middleware());

  return app;
}

/**
 * Setup application routes
 */
function setupRoutes(app) {
  // Health check (no authentication required)
  app.get('/health', AdminController.healthCheck);

  // Static routes
  app.get('/', StaticController.serveHome);
  app.get('/privacy.html', StaticController.servePrivacy);
  app.get('/terms.html', StaticController.serveTerms);
  app.get('/privacy', StaticController.redirectPrivacy);
  app.get('/terms', StaticController.redirectTerms);
  app.get('/css/:fileName', StaticController.serveCSS);
  app.get('/robots.txt', StaticController.serveRobots);
  app.get('/sitemap.xml', StaticController.serveSitemap);

  // Webhook routes
  app.get('/webhook', WebhookController.verifyWebhook);
  app.post('/webhook', 
    SecurityMiddleware.verifyWebhookSignature(),
    WebhookController.handleWebhook
  );

  // Langflow callback
  app.post('/langflow-callback', 
    SecurityMiddleware.validateInput([
      require('express-validator').body('session_id').notEmpty().withMessage('session_id is required')
    ]),
    WebhookController.handleLangflowCallback
  );

  // Admin routes (require authentication)
  const adminAuth = SecurityMiddleware.requireAdminAuth();
  
  app.get('/admin/status', adminAuth, AdminController.getStatus);
  
  app.get('/admin/test-profile/:userId', 
    adminAuth,
    SecurityMiddleware.validateInput(SecurityMiddleware.userIdValidation()),
    AdminController.testProfile
  );
  
  app.post('/admin/test-langflow',
    adminAuth,
    SecurityMiddleware.validateInput(SecurityMiddleware.messageValidation()),
    AdminController.testLangflow
  );

  app.get('/admin/cache', adminAuth, AdminController.getCacheInfo);
  app.post('/admin/cache/clear', adminAuth, AdminController.clearCache);

  app.get('/admin/config', adminAuth, AdminController.getConfig);
  app.get('/admin/stats', adminAuth, AdminController.getStats);
  app.get('/admin/logs', adminAuth, AdminController.getLogs);

  app.post('/admin/send-message',
    adminAuth,
    SecurityMiddleware.validateInput([
      require('express-validator').body('userId').notEmpty().withMessage('userId is required'),
      require('express-validator').body('message').notEmpty().withMessage('message is required')
    ]),
    AdminController.sendTestMessage
  );

  app.post('/admin/restart', adminAuth, AdminController.restartServices);

  // 404 handler for all other routes
  app.use(StaticController.handle404);
}

/**
 * Start the application server
 */
function startServer(app, port = null) {
  const serverPort = port || config.get('PORT');
  
  const server = app.listen(serverPort, () => {
    logger.success(`🚀 Secure Bridge Server Started on port ${serverPort}`);
    logger.info('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    logger.info(`🛡️  Security: Enhanced`);
    logger.info(`📍 Health: http://localhost:${serverPort}/health`);
    logger.info(`📄 Pages: / /privacy.html /terms.html (Allowed)`);
    logger.info(`🔑 Token: ${config.get('VERIFY_TOKEN')}`);
    logger.info(`📱 Facebook: ${config.get('PAGE_ACCESS_TOKEN') ? '✅ Connected' : '❌ Missing FB_TOKEN'}`);
    logger.info(`🤖 Langflow: ${config.get('LANGFLOW_API_URL') ? '✅ Connected' : '❌ Not configured'}`);
    logger.info(`🌍 Environment: ${config.get('NODE_ENV')}`);
    logger.info(`🐛 Debug Mode: ${config.isDebugMode() ? '✅ Enabled' : '❌ Disabled'}`);
    logger.info('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  });

  // Graceful shutdown handlers
  setupGracefulShutdown(server);

  return server;
}

/**
 * Setup graceful shutdown handlers
 */
function setupGracefulShutdown(server) {
  const gracefulShutdown = (signal) => {
    logger.info(`\n👋 ${signal} received, shutting down gracefully...`);
    
    server.close(() => {
      logger.success('✅ Server closed');
      process.exit(0);
    });
    
    // Force shutdown after 10 seconds
    setTimeout(() => {
      logger.warn('⚠️ Forcing shutdown...');
      process.exit(1);
    }, 10000);
  };

  process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
}

module.exports = {
  createApp,
  startServer
};
