{"name": "secure-facebook-bridge", "version": "2.0.0", "description": "Secure Facebook Messenger bridge to Langflow with enhanced security", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "node tests/basic-test.js", "docker:build": "docker build -t fb-langflow-bridge .", "docker:run": "docker run -p 3000:3000 --env-file .env fb-langflow-bridge", "docker:dev": "docker run -p 3000:3000 --env-file .env -v $(pwd):/app fb-langflow-bridge npm run dev", "lint": "echo \"Lin<PERSON> not configured\" && exit 0", "health": "curl -f http://localhost:3000/health || exit 1"}, "dependencies": {"express": "^4.18.2", "axios": "^1.6.0", "dotenv": "^16.3.1", "helmet": "^7.1.0", "cors": "^2.8.5", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["facebook", "messenger", "webhook", "langflow", "security", "chatbot"], "author": "Your Name", "license": "MIT", "engines": {"node": ">=16.0.0"}}