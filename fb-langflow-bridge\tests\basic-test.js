// tests/basic-test.js - Basic system tests
const http = require('http');
const { createApp } = require('../src/app');

/**
 * Basic test suite for the FB-Langflow Bridge
 */
class BasicTests {
  constructor() {
    this.app = null;
    this.server = null;
    this.port = 3001; // Use different port for testing
    this.baseUrl = `http://localhost:${this.port}`;
    this.results = [];
  }

  /**
   * Setup test environment
   */
  async setup() {
    console.log('🧪 Setting up test environment...');

    // Set test environment variables
    process.env.NODE_ENV = 'test';
    process.env.PORT = this.port.toString();
    process.env.VERIFY_TOKEN = 'test_verify_token_123456789';
    process.env.FB_TOKEN = 'test_fb_token_123456789';
    process.env.MOCK_FACEBOOK_API = 'true';
    process.env.MOCK_LANGFLOW_API = 'true';
    process.env.DEBUG_MODE = 'true';
    process.env.LOG_LEVEL = 'error'; // Reduce log noise during tests

    // Clear require cache to reload config with new env vars
    delete require.cache[require.resolve('../src/config')];
    delete require.cache[require.resolve('../src/app')];

    // Create app
    const { createApp } = require('../src/app');
    this.app = createApp();
    
    // Start server
    return new Promise((resolve, reject) => {
      this.server = this.app.listen(this.port, (err) => {
        if (err) {
          reject(err);
        } else {
          console.log(`✅ Test server started on port ${this.port}`);
          resolve();
        }
      });
    });
  }

  /**
   * Cleanup test environment
   */
  async cleanup() {
    console.log('🧹 Cleaning up test environment...');
    
    if (this.server) {
      return new Promise((resolve) => {
        this.server.close(() => {
          console.log('✅ Test server stopped');
          resolve();
        });
      });
    }
  }

  /**
   * Make HTTP request
   */
  async makeRequest(method, path, data = null, headers = {}) {
    return new Promise((resolve, reject) => {
      const options = {
        hostname: 'localhost',
        port: this.port,
        path: path,
        method: method,
        headers: {
          'Content-Type': 'application/json',
          ...headers
        }
      };

      const req = http.request(options, (res) => {
        let body = '';
        res.on('data', (chunk) => {
          body += chunk;
        });
        res.on('end', () => {
          try {
            const parsedBody = body ? JSON.parse(body) : {};
            resolve({
              statusCode: res.statusCode,
              headers: res.headers,
              body: parsedBody
            });
          } catch (error) {
            resolve({
              statusCode: res.statusCode,
              headers: res.headers,
              body: body
            });
          }
        });
      });

      req.on('error', reject);

      if (data) {
        req.write(JSON.stringify(data));
      }

      req.end();
    });
  }

  /**
   * Test health endpoint
   */
  async testHealth() {
    console.log('🔍 Testing health endpoint...');
    
    try {
      const response = await this.makeRequest('GET', '/health');
      
      const success = response.statusCode === 200 && 
                     response.body.status === 'healthy';
      
      this.results.push({
        test: 'Health Check',
        success: success,
        details: success ? 'OK' : `Status: ${response.statusCode}, Body: ${JSON.stringify(response.body)}`
      });
      
      return success;
    } catch (error) {
      this.results.push({
        test: 'Health Check',
        success: false,
        details: error.message
      });
      return false;
    }
  }

  /**
   * Test webhook verification
   */
  async testWebhookVerification() {
    console.log('🔍 Testing webhook verification...');

    try {
      const verifyToken = process.env.VERIFY_TOKEN;
      const challenge = 'test_challenge_123';
      const path = `/webhook?hub.mode=subscribe&hub.verify_token=${verifyToken}&hub.challenge=${challenge}`;

      console.log('Debug: Using verify token:', verifyToken);

      const response = await this.makeRequest('GET', path);

      const success = response.statusCode === 200 &&
                     response.body === challenge;

      this.results.push({
        test: 'Webhook Verification',
        success: success,
        details: success ? 'OK' : `Status: ${response.statusCode}, Body: ${JSON.stringify(response.body)}, Expected: ${challenge}`
      });

      return success;
    } catch (error) {
      this.results.push({
        test: 'Webhook Verification',
        success: false,
        details: error.message
      });
      return false;
    }
  }

  /**
   * Test webhook verification failure
   */
  async testWebhookVerificationFailure() {
    console.log('🔍 Testing webhook verification failure...');
    
    try {
      const wrongToken = 'wrong_token';
      const challenge = 'test_challenge_123';
      const path = `/webhook?hub.mode=subscribe&hub.verify_token=${wrongToken}&hub.challenge=${challenge}`;
      
      const response = await this.makeRequest('GET', path);
      
      const success = response.statusCode === 403;
      
      this.results.push({
        test: 'Webhook Verification Failure',
        success: success,
        details: success ? 'OK' : `Expected 403, got ${response.statusCode}`
      });
      
      return success;
    } catch (error) {
      this.results.push({
        test: 'Webhook Verification Failure',
        success: false,
        details: error.message
      });
      return false;
    }
  }

  /**
   * Test static file serving
   */
  async testStaticFiles() {
    console.log('🔍 Testing static file serving...');
    
    try {
      const response = await this.makeRequest('GET', '/');
      
      const success = response.statusCode === 200;
      
      this.results.push({
        test: 'Static File Serving',
        success: success,
        details: success ? 'OK' : `Status: ${response.statusCode}`
      });
      
      return success;
    } catch (error) {
      this.results.push({
        test: 'Static File Serving',
        success: false,
        details: error.message
      });
      return false;
    }
  }

  /**
   * Test 404 handling
   */
  async test404Handling() {
    console.log('🔍 Testing 404 handling...');
    
    try {
      const response = await this.makeRequest('GET', '/nonexistent-path');
      
      const success = response.statusCode === 404 && 
                     response.body.success === false;
      
      this.results.push({
        test: '404 Handling',
        success: success,
        details: success ? 'OK' : `Status: ${response.statusCode}, Body: ${JSON.stringify(response.body)}`
      });
      
      return success;
    } catch (error) {
      this.results.push({
        test: '404 Handling',
        success: false,
        details: error.message
      });
      return false;
    }
  }

  /**
   * Test admin authentication
   */
  async testAdminAuth() {
    console.log('🔍 Testing admin authentication...');
    
    try {
      // Test without API key
      const response1 = await this.makeRequest('GET', '/admin/status');
      const unauthorized = response1.statusCode === 401;
      
      // Test with wrong API key
      const response2 = await this.makeRequest('GET', '/admin/status', null, {
        'X-API-Key': 'wrong_key'
      });
      const wrongKey = response2.statusCode === 401;
      
      const success = unauthorized && wrongKey;
      
      this.results.push({
        test: 'Admin Authentication',
        success: success,
        details: success ? 'OK' : `Unauthorized: ${unauthorized}, Wrong key: ${wrongKey}`
      });
      
      return success;
    } catch (error) {
      this.results.push({
        test: 'Admin Authentication',
        success: false,
        details: error.message
      });
      return false;
    }
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🚀 Starting FB-Langflow Bridge Tests\n');
    
    try {
      await this.setup();
      
      // Run tests
      const tests = [
        this.testHealth(),
        this.testWebhookVerification(),
        this.testWebhookVerificationFailure(),
        this.testStaticFiles(),
        this.test404Handling(),
        this.testAdminAuth()
      ];
      
      await Promise.all(tests);
      
      // Print results
      this.printResults();
      
      // Check if all tests passed
      const allPassed = this.results.every(result => result.success);
      
      if (allPassed) {
        console.log('\n✅ All tests passed!');
        process.exit(0);
      } else {
        console.log('\n❌ Some tests failed!');
        process.exit(1);
      }
      
    } catch (error) {
      console.error('❌ Test setup failed:', error.message);
      process.exit(1);
    } finally {
      await this.cleanup();
    }
  }

  /**
   * Print test results
   */
  printResults() {
    console.log('\n📊 Test Results:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    
    this.results.forEach(result => {
      const status = result.success ? '✅' : '❌';
      console.log(`${status} ${result.test}: ${result.details}`);
    });
    
    const passed = this.results.filter(r => r.success).length;
    const total = this.results.length;
    
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log(`📈 Results: ${passed}/${total} tests passed`);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const tests = new BasicTests();
  tests.runAllTests();
}

module.exports = BasicTests;
