// src/utils/errorHandler.js - Centralized error handling
const logger = require('./logger');
const config = require('../config');

/**
 * Custom error classes
 */
class AppError extends Error {
  constructor(message, statusCode = 500, code = 'INTERNAL_ERROR') {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

class ValidationError extends AppError {
  constructor(message, details = []) {
    super(message, 400, 'VALIDATION_ERROR');
    this.details = details;
  }
}

class AuthenticationError extends AppError {
  constructor(message = 'Authentication failed') {
    super(message, 401, 'AUTHENTICATION_ERROR');
  }
}

class AuthorizationError extends AppError {
  constructor(message = 'Access denied') {
    super(message, 403, 'AUTHORIZATION_ERROR');
  }
}

class NotFoundError extends AppError {
  constructor(message = 'Resource not found') {
    super(message, 404, 'NOT_FOUND_ERROR');
  }
}

class RateLimitError extends AppError {
  constructor(message = 'Too many requests') {
    super(message, 429, 'RATE_LIMIT_ERROR');
  }
}

class ExternalServiceError extends AppError {
  constructor(service, message = 'External service error') {
    super(`${service}: ${message}`, 502, 'EXTERNAL_SERVICE_ERROR');
    this.service = service;
  }
}

/**
 * Error handler utility
 */
class ErrorHandler {
  /**
   * Handle operational errors
   */
  static handleError(error, req = null, res = null) {
    logger.error('Error occurred:', {
      message: error.message,
      stack: error.stack,
      code: error.code,
      statusCode: error.statusCode,
      url: req?.originalUrl,
      method: req?.method,
      ip: req?.ip,
      userAgent: req?.get('User-Agent')
    });

    // Send response if res object is provided
    if (res && !res.headersSent) {
      this.sendErrorResponse(error, res);
    }

    // Log security events
    if (error.statusCode === 401 || error.statusCode === 403) {
      logger.security('Access attempt denied', {
        error: error.message,
        ip: req?.ip,
        url: req?.originalUrl
      });
    }
  }

  /**
   * Send error response to client
   */
  static sendErrorResponse(error, res) {
    const statusCode = error.statusCode || 500;
    const isDevelopment = config.isDevelopment();

    const errorResponse = {
      success: false,
      error: {
        message: error.message,
        code: error.code || 'INTERNAL_ERROR',
        timestamp: new Date().toISOString()
      }
    };

    // Add details for validation errors
    if (error instanceof ValidationError && error.details) {
      errorResponse.error.details = error.details;
    }

    // Add stack trace in development
    if (isDevelopment && error.stack) {
      errorResponse.error.stack = error.stack;
    }

    // Add request ID if available
    if (res.locals.requestId) {
      errorResponse.error.requestId = res.locals.requestId;
    }

    res.status(statusCode).json(errorResponse);
  }

  /**
   * Express error middleware
   */
  static middleware() {
    return (error, req, res, next) => {
      // Handle validation errors from express-validator
      if (error.type === 'entity.parse.failed') {
        error = new ValidationError('Invalid JSON payload');
      }

      // Handle rate limit errors
      if (error.type === 'rate-limit') {
        error = new RateLimitError(error.message);
      }

      this.handleError(error, req, res);
    };
  }

  /**
   * Async wrapper for route handlers
   */
  static asyncWrapper(fn) {
    return (req, res, next) => {
      Promise.resolve(fn(req, res, next)).catch(next);
    };
  }

  /**
   * Handle Facebook API errors
   */
  static handleFacebookError(error, context = '') {
    const fbError = error.response?.data?.error;
    
    if (fbError) {
      const message = `Facebook API Error: ${fbError.message || error.message}`;
      logger.error(message, {
        context,
        code: fbError.code,
        type: fbError.type,
        fbtrace_id: fbError.fbtrace_id
      });
      
      throw new ExternalServiceError('Facebook', fbError.message);
    }
    
    throw new ExternalServiceError('Facebook', error.message);
  }

  /**
   * Handle Langflow API errors
   */
  static handleLangflowError(error, context = '') {
    const message = `Langflow API Error: ${error.message}`;
    logger.error(message, {
      context,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    });
    
    throw new ExternalServiceError('Langflow', error.message);
  }

  /**
   * Handle webhook signature verification errors
   */
  static handleWebhookError(error, req) {
    logger.security('Webhook verification failed', {
      error: error.message,
      ip: req.ip,
      headers: req.headers,
      signature: req.headers['x-hub-signature-256']
    });
    
    throw new AuthenticationError('Invalid webhook signature');
  }

  /**
   * Handle uncaught exceptions
   */
  static handleUncaughtException() {
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', {
        message: error.message,
        stack: error.stack
      });
      
      // Graceful shutdown
      process.exit(1);
    });
  }

  /**
   * Handle unhandled promise rejections
   */
  static handleUnhandledRejection() {
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection:', {
        reason: reason?.message || reason,
        stack: reason?.stack,
        promise: promise.toString()
      });
      
      // Graceful shutdown
      process.exit(1);
    });
  }

  /**
   * Initialize global error handlers
   */
  static initialize() {
    this.handleUncaughtException();
    this.handleUnhandledRejection();
  }
}

module.exports = {
  ErrorHandler,
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  RateLimitError,
  ExternalServiceError
};
