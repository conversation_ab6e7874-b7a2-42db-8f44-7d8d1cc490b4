# FB-Langflow Bridge Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# REQUIRED CONFIGURATION
# =============================================================================

# Facebook Configuration
VERIFY_TOKEN=your_facebook_verify_token_here
FB_TOKEN=your_facebook_page_access_token_here

# Langflow Configuration
LANGFLOW_API_URL=https://your-langflow-instance.com/api/v1/webhook/your-flow-id
LANGFLOW_BASE_URL=https://your-langflow-instance.com

# Security Configuration (REQUIRED IN PRODUCTION)
WEBHOOK_SECRET=your_webhook_secret_32_chars_minimum
ADMIN_API_KEY=your_admin_api_key_32_chars_minimum
JWT_SECRET=your_jwt_secret_32_chars_minimum
ENCRYPTION_KEY=your_encryption_key_32_chars_minimum

# =============================================================================
# NETWORK CONFIGURATION
# =============================================================================

# Application URL
BASE_URL=https://your-app-domain.com

# CORS Origins (comma-separated)
ALLOWED_ORIGINS=https://your-app-domain.com,https://www.your-app-domain.com

# Trusted IPs for IP whitelisting (comma-separated, optional)
TRUSTED_IPS=127.0.0.1,::1

# =============================================================================
# OPTIONAL CONFIGURATION
# =============================================================================

# Environment
NODE_ENV=production
PORT=3000

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100
RATE_LIMIT_WEBHOOK_MAX=1000

# Message Configuration
MAX_MESSAGE_LENGTH=2000

# Cache Configuration
CACHE_TTL=86400000
CACHE_MAX_SIZE=1000

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=combined
ENABLE_REQUEST_LOGGING=true

# Security Features
ENABLE_WEBHOOK_SIGNATURE_VERIFICATION=true
ENABLE_IP_WHITELIST=false
ENABLE_REQUEST_SIGNING=false

# Development/Debug
DEBUG_MODE=false
MOCK_FACEBOOK_API=false
MOCK_LANGFLOW_API=false

# =============================================================================
# DEVELOPMENT ONLY
# =============================================================================

# For local development, you can use these simplified settings:
# NODE_ENV=development
# DEBUG_MODE=true
# ENABLE_WEBHOOK_SIGNATURE_VERIFICATION=false
# LOG_LEVEL=debug

# =============================================================================
# STAGING ENVIRONMENT
# =============================================================================

# For staging, prefix variables with STAGING_
# STAGING_VERIFY_TOKEN=staging_verify_token
# STAGING_FB_TOKEN=staging_page_access_token
# STAGING_LANGFLOW_API_URL=https://staging-langflow.com/api/v1/webhook/flow-id
# STAGING_LANGFLOW_BASE_URL=https://staging-langflow.com
# STAGING_WEBHOOK_SECRET=staging_webhook_secret_32_chars
# STAGING_ADMIN_API_KEY=staging_admin_api_key_32_chars
# STAGING_JWT_SECRET=staging_jwt_secret_32_chars
# STAGING_ENCRYPTION_KEY=staging_encryption_key_32_chars
# STAGING_BASE_URL=https://staging-your-app.com
# STAGING_ALLOWED_ORIGINS=https://staging-your-app.com

# =============================================================================
# SECURITY NOTES
# =============================================================================

# 1. Never commit this file with real values to version control
# 2. Use strong, randomly generated secrets (32+ characters)
# 3. Rotate secrets regularly in production
# 4. Use different secrets for different environments
# 5. Store secrets securely in your deployment platform
# 6. Enable webhook signature verification in production
# 7. Consider enabling IP whitelisting for admin endpoints
# 8. Use HTTPS in production (BASE_URL should start with https://)

# =============================================================================
# QUICK SETUP GUIDE
# =============================================================================

# 1. Copy this file: cp .env.example .env
# 2. Fill in your Facebook tokens from Facebook Developer Console
# 3. Set up your Langflow instance and get the webhook URL
# 4. Generate secure secrets (you can use: node -e "console.log(require('crypto').randomBytes(32).toString('hex'))")
# 5. Update BASE_URL to your actual domain
# 6. Configure ALLOWED_ORIGINS for CORS
# 7. Test locally: npm run dev
# 8. Deploy to production with proper secrets management
