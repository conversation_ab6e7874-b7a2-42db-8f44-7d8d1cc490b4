// src/controllers/staticController.js - Static file serving controller
const path = require('path');
const logger = require('../utils/logger');
const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NotFoundError } = require('../utils/errorHandler');

/**
 * Static controller for serving static files and pages
 */
class StaticController {
  /**
   * Serve home page (GET /)
   */
  static async serveHome(req, res) {
    try {
      const filePath = path.join(__dirname, '../../public', 'index.html');

      res.sendFile(filePath, (err) => {
        if (err) {
          logger.error('Error serving index.html', {
            error: err.message,
            path: filePath
          });
          throw new NotFoundError('Home page not found');
        }
      });

    } catch (error) {
      ErrorHandler.handleError(error, req, res);
    }
  }

  /**
   * Serve privacy policy (GET /privacy.html)
   */
  static async servePrivacy(req, res) {
    try {
      const filePath = path.join(__dirname, '../../public', 'privacy.html');

      res.sendFile(filePath, (err) => {
        if (err) {
          logger.error('Error serving privacy.html', {
            error: err.message,
            path: filePath
          });
          throw new NotFoundError('Privacy policy not found');
        }
      });

    } catch (error) {
      ErrorHandler.handleError(error, req, res);
    }
  }

  /**
   * Serve terms of service (GET /terms.html)
   */
  static async serveTerms(req, res) {
    try {
      const filePath = path.join(__dirname, '../../public', 'terms.html');

      res.sendFile(filePath, (err) => {
        if (err) {
          logger.error('Error serving terms.html', {
            error: err.message,
            path: filePath
          });
          throw new NotFoundError('Terms of service not found');
        }
      });

    } catch (error) {
      ErrorHandler.handleError(error, req, res);
    }
  }

  /**
   * Redirect privacy (GET /privacy)
   */
  static redirectPrivacy(req, res) {
    res.redirect(301, '/privacy.html');
  }

  /**
   * Redirect terms (GET /terms)
   */
  static redirectTerms(req, res) {
    res.redirect(301, '/terms.html');
  }

  /**
   * Serve CSS files (GET /css/*)
   */
  static async serveCSS(req, res) {
    try {
      const fileName = req.params.fileName;
      const allowedFiles = ['style.css'];

      if (!allowedFiles.includes(fileName)) {
        throw new NotFoundError('CSS file not found');
      }

      const filePath = path.join(__dirname, '../../public/css', fileName);

      res.setHeader('Content-Type', 'text/css');
      res.setHeader('Cache-Control', 'public, max-age=86400'); // 24 hours cache

      res.sendFile(filePath, (err) => {
        if (err) {
          logger.error('Error serving CSS file', {
            error: err.message,
            fileName,
            path: filePath
          });
          throw new NotFoundError('CSS file not found');
        }
      });

    } catch (error) {
      ErrorHandler.handleError(error, req, res);
    }
  }

  /**
   * Handle 404 for static files
   */
  static handle404(req, res) {
    // Don't log 404s for common bot/crawler requests
    const skipLogging = req.originalUrl.match(/\.(php|asp|aspx|jsp|cgi)$/i) ||
                       req.originalUrl.includes('wp-') ||
                       req.originalUrl.includes('admin') ||
                       req.originalUrl.includes('robots.txt') ||
                       req.originalUrl.includes('favicon.ico');

    if (!skipLogging) {
      logger.warn('404 - Page not found', {
        method: req.method,
        url: req.originalUrl,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
    }

    res.status(404).json({
      success: false,
      error: {
        message: 'Page not found',
        code: 'NOT_FOUND',
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Security headers for static files
   */
  static setSecurityHeaders(req, res, next) {
    // Set security headers for static files
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'SAMEORIGIN');

    // Set appropriate content type based on file extension
    const ext = path.extname(req.path).toLowerCase();

    switch (ext) {
      case '.html':
        res.setHeader('Content-Type', 'text/html; charset=utf-8');
        break;
      case '.css':
        res.setHeader('Content-Type', 'text/css; charset=utf-8');
        break;
      case '.js':
        res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
        break;
      case '.json':
        res.setHeader('Content-Type', 'application/json; charset=utf-8');
        break;
      case '.png':
        res.setHeader('Content-Type', 'image/png');
        break;
      case '.jpg':
      case '.jpeg':
        res.setHeader('Content-Type', 'image/jpeg');
        break;
      case '.gif':
        res.setHeader('Content-Type', 'image/gif');
        break;
      case '.svg':
        res.setHeader('Content-Type', 'image/svg+xml');
        break;
      case '.ico':
        res.setHeader('Content-Type', 'image/x-icon');
        break;
    }

    next();
  }

  /**
   * Check if file is allowed to be served
   */
  static isAllowedFile(fileName) {
    const allowedFiles = [
      'index.html',
      'privacy.html',
      'terms.html',
      'style.css',
      'script.js',
      'favicon.ico'
    ];

    return allowedFiles.includes(fileName);
  }

  /**
   * Serve robots.txt
   */
  static serveRobots(req, res) {
    const robotsTxt = `User-agent: *
Disallow: /admin/
Disallow: /webhook
Disallow: /langflow-callback
Allow: /
Allow: /privacy.html
Allow: /terms.html

Sitemap: ${req.protocol}://${req.get('host')}/sitemap.xml`;

    res.setHeader('Content-Type', 'text/plain');
    res.send(robotsTxt);
  }

  /**
   * Serve sitemap.xml
   */
  static serveSitemap(req, res) {
    const baseUrl = `${req.protocol}://${req.get('host')}`;
    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${baseUrl}/</loc>
    <changefreq>monthly</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>${baseUrl}/privacy.html</loc>
    <changefreq>yearly</changefreq>
    <priority>0.5</priority>
  </url>
  <url>
    <loc>${baseUrl}/terms.html</loc>
    <changefreq>yearly</changefreq>
    <priority>0.5</priority>
  </url>
</urlset>`;

    res.setHeader('Content-Type', 'application/xml');
    res.send(sitemap);
  }
}

module.exports = StaticController;