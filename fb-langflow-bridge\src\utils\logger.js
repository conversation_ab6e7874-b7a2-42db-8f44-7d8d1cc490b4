// src/utils/logger.js - Enhanced logging utility
const config = require('../config');

/**
 * Enhanced logger with different levels and formatting
 */
class Logger {
  constructor() {
    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3
    };
    
    this.currentLevel = this.levels[config.get('LOG_LEVEL')] || this.levels.info;
    this.enableColors = !config.isProduction();
  }

  /**
   * Color codes for console output
   */
  getColors() {
    return {
      error: '\x1b[31m',   // Red
      warn: '\x1b[33m',    // Yellow
      info: '\x1b[36m',    // Cyan
      debug: '\x1b[35m',   // Magenta
      success: '\x1b[32m', // Green
      reset: '\x1b[0m'     // Reset
    };
  }

  /**
   * Format log message with timestamp and level
   */
  formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const colors = this.getColors();
    
    let formattedMessage = `[${timestamp}] [${level.toUpperCase()}]`;
    
    if (this.enableColors) {
      formattedMessage = `${colors[level]}${formattedMessage}${colors.reset}`;
    }
    
    formattedMessage += ` ${message}`;
    
    // Add metadata if provided
    if (Object.keys(meta).length > 0) {
      formattedMessage += ` ${JSON.stringify(meta)}`;
    }
    
    return formattedMessage;
  }

  /**
   * Check if level should be logged
   */
  shouldLog(level) {
    return this.levels[level] <= this.currentLevel;
  }

  /**
   * Log error messages
   */
  error(message, meta = {}) {
    if (this.shouldLog('error')) {
      console.error(this.formatMessage('error', message, meta));
    }
  }

  /**
   * Log warning messages
   */
  warn(message, meta = {}) {
    if (this.shouldLog('warn')) {
      console.warn(this.formatMessage('warn', message, meta));
    }
  }

  /**
   * Log info messages
   */
  info(message, meta = {}) {
    if (this.shouldLog('info')) {
      console.log(this.formatMessage('info', message, meta));
    }
  }

  /**
   * Log debug messages
   */
  debug(message, meta = {}) {
    if (this.shouldLog('debug')) {
      console.log(this.formatMessage('debug', message, meta));
    }
  }

  /**
   * Log success messages (always shown)
   */
  success(message, meta = {}) {
    const colors = this.getColors();
    let formattedMessage = this.formatMessage('info', message, meta);
    
    if (this.enableColors) {
      formattedMessage = formattedMessage.replace('[INFO]', `${colors.success}[SUCCESS]${colors.reset}`);
    }
    
    console.log(formattedMessage);
  }

  /**
   * Log HTTP requests
   */
  request(req, res, responseTime) {
    if (!config.get('ENABLE_REQUEST_LOGGING')) {
      return;
    }

    const { method, originalUrl, ip } = req;
    const { statusCode } = res;
    
    const message = `${method} ${originalUrl} ${statusCode} ${responseTime}ms`;
    const meta = {
      ip,
      userAgent: req.get('User-Agent'),
      contentLength: res.get('Content-Length')
    };

    if (statusCode >= 400) {
      this.warn(message, meta);
    } else {
      this.info(message, meta);
    }
  }

  /**
   * Log Facebook webhook events
   */
  webhook(event, meta = {}) {
    const timestamp = new Date().toISOString().slice(11, 19);
    const senderId = event.sender?.id;
    const messageText = event.message?.text;
    
    if (messageText && !event.message.is_echo) {
      this.info(`[${timestamp}] 💬 ${senderId}: "${messageText.substring(0, 100)}${messageText.length > 100 ? '...' : ''}"`, meta);
    }
  }

  /**
   * Log Langflow interactions
   */
  langflow(action, data = {}) {
    const message = `🤖 Langflow ${action}`;
    this.info(message, data);
  }

  /**
   * Log Facebook API calls
   */
  facebook(action, data = {}) {
    const message = `📱 Facebook ${action}`;
    this.info(message, data);
  }

  /**
   * Log security events
   */
  security(event, data = {}) {
    const message = `🛡️ Security: ${event}`;
    this.warn(message, data);
  }

  /**
   * Log performance metrics
   */
  performance(metric, value, unit = 'ms') {
    if (config.isDebugMode()) {
      this.debug(`⚡ Performance: ${metric} = ${value}${unit}`);
    }
  }

  /**
   * Log cache operations
   */
  cache(operation, key, data = {}) {
    if (config.isDebugMode()) {
      this.debug(`📋 Cache ${operation}: ${key}`, data);
    }
  }
}

// Export singleton instance
const logger = new Logger();
module.exports = logger;
