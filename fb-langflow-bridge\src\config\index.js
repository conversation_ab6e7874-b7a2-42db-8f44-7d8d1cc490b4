// src/config/index.js - Enhanced configuration management
require('dotenv').config();
const crypto = require('crypto');

/**
 * Configuration validation and management
 * Provides centralized config with strong validation and security
 */
class ConfigManager {
  constructor() {
    this.config = this.loadConfig();
    this.validateConfig();
  }

  /**
   * Load configuration from environment variables
   */
  loadConfig() {
    const toInt = (val, fallback) => {
      const n = parseInt(val, 10);
      return Number.isNaN(n) ? fallback : n;
    };

    const toBool = (val, fallback = false) => {
      if (typeof val === 'string') {
        return val.toLowerCase() === 'true';
      }
      return fallback;
    };

    return {
      // Environment
      NODE_ENV: process.env.NODE_ENV?.trim() || 'development',
      PORT: toInt(process.env.PORT, 3000),
      
      // Facebook Configuration
      VERIFY_TOKEN: process.env.VERIFY_TOKEN?.trim() || '',
      PAGE_ACCESS_TOKEN: process.env.FB_TOKEN?.trim() || '',
      
      // Langflow Configuration
      LANGFLOW_API_URL: process.env.LANGFLOW_API_URL?.trim() || 
        'https://d1r6o0uhss8x1l.cloudfront.net/api/v1/webhook/85f37591-2308-4908-8a8d-22ccf60a8f6a',
      LANGFLOW_BASE_URL: process.env.LANGFLOW_BASE_URL?.trim() || 
        'https://d1r6o0uhss8x1l.cloudfront.net',
      
      // Security Configuration
      WEBHOOK_SECRET: process.env.WEBHOOK_SECRET?.trim() || '',
      ADMIN_API_KEY: process.env.ADMIN_API_KEY?.trim() || '',
      JWT_SECRET: process.env.JWT_SECRET?.trim() || '',
      ENCRYPTION_KEY: process.env.ENCRYPTION_KEY?.trim() || '',
      
      // Network Configuration
      BASE_URL: process.env.BASE_URL?.trim() || 'http://localhost:3000',
      ALLOWED_ORIGINS: process.env.ALLOWED_ORIGINS
        ? process.env.ALLOWED_ORIGINS.split(',').map(s => s.trim())
        : ['http://localhost:3000'],
      TRUSTED_IPS: process.env.TRUSTED_IPS
        ? process.env.TRUSTED_IPS.split(',').map(s => s.trim())
        : [],
      
      // Rate Limiting
      RATE_LIMIT_WINDOW: toInt(process.env.RATE_LIMIT_WINDOW, 15 * 60 * 1000), // 15 minutes
      RATE_LIMIT_MAX: toInt(process.env.RATE_LIMIT_MAX, 100),
      RATE_LIMIT_WEBHOOK_MAX: toInt(process.env.RATE_LIMIT_WEBHOOK_MAX, 1000),
      
      // Message Configuration
      MAX_MESSAGE_LENGTH: toInt(process.env.MAX_MESSAGE_LENGTH, 2000),
      
      // Cache Configuration
      CACHE_TTL: toInt(process.env.CACHE_TTL, 24 * 60 * 60 * 1000), // 24 hours
      CACHE_MAX_SIZE: toInt(process.env.CACHE_MAX_SIZE, 1000),
      
      // Logging Configuration
      LOG_LEVEL: process.env.LOG_LEVEL?.trim() || 'info',
      LOG_FORMAT: process.env.LOG_FORMAT?.trim() || 'combined',
      ENABLE_REQUEST_LOGGING: toBool(process.env.ENABLE_REQUEST_LOGGING, true),
      
      // Security Features
      ENABLE_WEBHOOK_SIGNATURE_VERIFICATION: toBool(process.env.ENABLE_WEBHOOK_SIGNATURE_VERIFICATION, true),
      ENABLE_IP_WHITELIST: toBool(process.env.ENABLE_IP_WHITELIST, false),
      ENABLE_REQUEST_SIGNING: toBool(process.env.ENABLE_REQUEST_SIGNING, false),
      
      // Development/Debug
      DEBUG_MODE: toBool(process.env.DEBUG_MODE, false),
      MOCK_FACEBOOK_API: toBool(process.env.MOCK_FACEBOOK_API, false),
      MOCK_LANGFLOW_API: toBool(process.env.MOCK_LANGFLOW_API, false),
    };
  }

  /**
   * Validate configuration based on environment
   */
  validateConfig() {
    const errors = [];
    const warnings = [];

    // Environment-specific validation
    if (this.config.NODE_ENV === 'production') {
      this.validateProductionConfig(errors, warnings);
    } else {
      this.validateDevelopmentConfig(errors, warnings);
    }

    // Common validation
    this.validateCommonConfig(errors, warnings);

    // Handle validation results
    if (errors.length > 0) {
      console.error('❌ Configuration validation failed:');
      errors.forEach(error => console.error(`  - ${error}`));
      process.exit(1);
    }

    if (warnings.length > 0) {
      console.warn('⚠️ Configuration warnings:');
      warnings.forEach(warning => console.warn(`  - ${warning}`));
    }

    console.log('✅ Configuration validation passed');
  }

  /**
   * Validate production-specific configuration
   */
  validateProductionConfig(errors, warnings) {
    const required = [
      'VERIFY_TOKEN',
      'PAGE_ACCESS_TOKEN', 
      'WEBHOOK_SECRET',
      'ADMIN_API_KEY',
      'JWT_SECRET',
      'ENCRYPTION_KEY'
    ];

    required.forEach(key => {
      if (!this.config[key] || this.config[key].length === 0) {
        errors.push(`Missing required environment variable: ${key}`);
      }
    });

    // Security validations
    if (this.config.WEBHOOK_SECRET.length < 32) {
      errors.push('WEBHOOK_SECRET must be at least 32 characters in production');
    }

    if (this.config.ADMIN_API_KEY.length < 32) {
      errors.push('ADMIN_API_KEY must be at least 32 characters in production');
    }

    if (this.config.JWT_SECRET.length < 32) {
      errors.push('JWT_SECRET must be at least 32 characters in production');
    }

    if (this.config.ENCRYPTION_KEY.length < 32) {
      errors.push('ENCRYPTION_KEY must be at least 32 characters in production');
    }

    // Network security
    if (this.config.ALLOWED_ORIGINS.includes('*')) {
      warnings.push('Wildcard CORS origin detected in production');
    }

    if (!this.config.BASE_URL.startsWith('https://')) {
      warnings.push('BASE_URL should use HTTPS in production');
    }
  }

  /**
   * Validate development-specific configuration
   */
  validateDevelopmentConfig(errors, warnings) {
    const required = ['VERIFY_TOKEN', 'PAGE_ACCESS_TOKEN'];

    required.forEach(key => {
      if (!this.config[key] || this.config[key].length === 0) {
        errors.push(`Missing required environment variable: ${key}`);
      }
    });

    // Set development defaults
    if (!this.config.WEBHOOK_SECRET) {
      this.config.WEBHOOK_SECRET = this.generateSecureToken(32);
      warnings.push('Using generated WEBHOOK_SECRET for development');
    }

    if (!this.config.ADMIN_API_KEY) {
      this.config.ADMIN_API_KEY = this.generateSecureToken(32);
      warnings.push('Using generated ADMIN_API_KEY for development');
    }

    if (!this.config.JWT_SECRET) {
      this.config.JWT_SECRET = this.generateSecureToken(32);
      warnings.push('Using generated JWT_SECRET for development');
    }

    if (!this.config.ENCRYPTION_KEY) {
      this.config.ENCRYPTION_KEY = this.generateSecureToken(32);
      warnings.push('Using generated ENCRYPTION_KEY for development');
    }
  }

  /**
   * Validate common configuration
   */
  validateCommonConfig(errors, warnings) {
    // Token format validation
    if (this.config.VERIFY_TOKEN.length < 10) {
      errors.push(`VERIFY_TOKEN too short: ${this.config.VERIFY_TOKEN.length} characters (minimum 10 required)`);
    }

    // URL validation
    try {
      new URL(this.config.LANGFLOW_API_URL);
      new URL(this.config.LANGFLOW_BASE_URL);
      new URL(this.config.BASE_URL);
    } catch (error) {
      errors.push('Invalid URL format in configuration');
    }

    // Numeric validation
    if (this.config.PORT < 1 || this.config.PORT > 65535) {
      errors.push('PORT must be between 1 and 65535');
    }

    if (this.config.MAX_MESSAGE_LENGTH < 1 || this.config.MAX_MESSAGE_LENGTH > 10000) {
      warnings.push('MAX_MESSAGE_LENGTH should be between 1 and 10000');
    }
  }

  /**
   * Generate secure random token
   */
  generateSecureToken(length = 32) {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Get configuration value
   */
  get(key) {
    return this.config[key];
  }

  /**
   * Get all configuration (for debugging)
   */
  getAll() {
    // Return sanitized config (hide sensitive values)
    const sanitized = { ...this.config };
    const sensitiveKeys = [
      'PAGE_ACCESS_TOKEN',
      'WEBHOOK_SECRET', 
      'ADMIN_API_KEY',
      'JWT_SECRET',
      'ENCRYPTION_KEY'
    ];

    sensitiveKeys.forEach(key => {
      if (sanitized[key]) {
        sanitized[key] = `${sanitized[key].substring(0, 4)}****${sanitized[key].substring(sanitized[key].length - 4)}`;
      }
    });

    return sanitized;
  }

  /**
   * Check if running in production
   */
  isProduction() {
    return this.config.NODE_ENV === 'production';
  }

  /**
   * Check if running in development
   */
  isDevelopment() {
    return this.config.NODE_ENV === 'development';
  }

  /**
   * Check if debug mode is enabled
   */
  isDebugMode() {
    return this.config.DEBUG_MODE;
  }
}

// Export singleton instance
const configManager = new ConfigManager();
module.exports = configManager;
