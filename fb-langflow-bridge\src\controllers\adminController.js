// src/controllers/adminController.js - Admin API controller
const config = require('../config');
const logger = require('../utils/logger');
const { ErrorHandler } = require('../utils/errorHandler');
const facebookService = require('../services/facebookService');
const langflowService = require('../services/langflowService');
const cacheService = require('../services/cacheService');

/**
 * Admin controller for system management and monitoring
 */
class AdminController {
  /**
   * Get system status (GET /admin/status)
   */
  static async getStatus(req, res) {
    try {
      const [facebookHealth, langflowHealth] = await Promise.allSettled([
        facebookService.healthCheck(),
        langflowService.healthCheck()
      ]);

      const cacheHealth = cacheService.healthCheck();
      const memoryUsage = process.memoryUsage();

      const status = {
        system: {
          status: 'running',
          environment: config.get('NODE_ENV'),
          uptime: process.uptime(),
          timestamp: new Date().toISOString(),
          version: '2.0.0'
        },
        services: {
          facebook: facebookHealth.status === 'fulfilled' ? facebookHealth.value : { status: 'error', error: facebookHealth.reason?.message },
          langflow: langflowHealth.status === 'fulfilled' ? langflowHealth.value : { status: 'error', error: langflowHealth.reason?.message },
          cache: cacheHealth
        },
        resources: {
          memory: {
            used: `${(memoryUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`,
            total: `${(memoryUsage.heapTotal / 1024 / 1024).toFixed(2)} MB`,
            external: `${(memoryUsage.external / 1024 / 1024).toFixed(2)} MB`,
            rss: `${(memoryUsage.rss / 1024 / 1024).toFixed(2)} MB`
          },
          cache: cacheService.getStats()
        },
        configuration: {
          debugMode: config.isDebugMode(),
          mockFacebook: config.get('MOCK_FACEBOOK_API'),
          mockLangflow: config.get('MOCK_LANGFLOW_API'),
          rateLimitMax: config.get('RATE_LIMIT_MAX'),
          maxMessageLength: config.get('MAX_MESSAGE_LENGTH')
        }
      };

      res.json(status);
      
    } catch (error) {
      ErrorHandler.handleError(error, req, res);
    }
  }

  /**
   * Test Facebook profile fetch (GET /admin/test-profile/:userId)
   */
  static async testProfile(req, res) {
    try {
      const { userId } = req.params;
      
      logger.info('Testing profile fetch', { 
        userId: userId.substring(0, 8) + '...',
        requestedBy: req.ip 
      });

      const profile = await facebookService.getUserProfile(userId);
      
      res.json({
        success: true,
        profile: profile,
        cached: !!cacheService.getUserProfile(userId),
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      logger.error('Profile test failed', {
        userId: req.params.userId?.substring(0, 8) + '...',
        error: error.message
      });
      
      ErrorHandler.handleError(error, req, res);
    }
  }

  /**
   * Test Langflow integration (POST /admin/test-langflow)
   */
  static async testLangflow(req, res) {
    try {
      const { message = "Test message", userId = "test-user" } = req.body;
      
      logger.info('Testing Langflow integration', {
        message: message.substring(0, 50),
        userId: userId.substring(0, 8) + '...',
        requestedBy: req.ip
      });

      let userProfile = null;
      if (userId !== "test-user") {
        userProfile = await facebookService.getUserProfile(userId);
      }

      const result = await langflowService.triggerWorkflow(message, userId, userProfile);
      
      res.json({
        success: true,
        input: {
          message,
          userId: userId.substring(0, 8) + '...',
          hasProfile: !!userProfile
        },
        result: result,
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      logger.error('Langflow test failed', {
        message: req.body.message?.substring(0, 50),
        error: error.message
      });
      
      ErrorHandler.handleError(error, req, res);
    }
  }

  /**
   * Get cache information (GET /admin/cache)
   */
  static async getCacheInfo(req, res) {
    try {
      const stats = cacheService.getStats();
      const keys = cacheService.getKeys();
      
      res.json({
        success: true,
        stats: stats,
        keys: keys.slice(0, 50), // Limit to first 50 keys
        totalKeys: keys.length,
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      ErrorHandler.handleError(error, req, res);
    }
  }

  /**
   * Clear cache (POST /admin/cache/clear)
   */
  static async clearCache(req, res) {
    try {
      const { type = 'all' } = req.body;
      let cleared = 0;
      
      logger.info('Cache clear requested', {
        type,
        requestedBy: req.ip,
        currentSize: cacheService.getStats().size
      });

      if (type === 'all') {
        cleared = cacheService.clear();
      } else if (type === 'expired') {
        cleared = cacheService.cleanup();
      } else if (type === 'profiles') {
        // Clear only user profiles
        const keys = cacheService.getKeys();
        keys.forEach(keyInfo => {
          if (keyInfo.key.startsWith('user_profile:')) {
            cacheService.delete(keyInfo.key);
            cleared++;
          }
        });
      }

      res.json({
        success: true,
        message: `Cache cleared: ${cleared} entries removed`,
        cleared: cleared,
        type: type,
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      ErrorHandler.handleError(error, req, res);
    }
  }

  /**
   * Get system logs (GET /admin/logs)
   */
  static async getLogs(req, res) {
    try {
      const { level = 'info', limit = 100 } = req.query;
      
      // This is a placeholder - in a real implementation, you'd read from log files
      // or use a logging service that supports querying
      
      res.json({
        success: true,
        message: 'Log retrieval not implemented - check server console or log files',
        parameters: { level, limit },
        suggestion: 'Use external log aggregation service for production',
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      ErrorHandler.handleError(error, req, res);
    }
  }

  /**
   * Get configuration (GET /admin/config)
   */
  static async getConfig(req, res) {
    try {
      const sanitizedConfig = config.getAll();
      
      res.json({
        success: true,
        config: sanitizedConfig,
        environment: config.get('NODE_ENV'),
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      ErrorHandler.handleError(error, req, res);
    }
  }

  /**
   * Send test message (POST /admin/send-message)
   */
  static async sendTestMessage(req, res) {
    try {
      const { userId, message } = req.body;
      
      if (!userId || !message) {
        return res.status(400).json({
          success: false,
          error: 'userId and message are required'
        });
      }

      logger.info('Sending test message', {
        userId: userId.substring(0, 8) + '...',
        messageLength: message.length,
        requestedBy: req.ip
      });

      const result = await facebookService.sendTextMessage(userId, message);
      
      res.json({
        success: true,
        result: result,
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      ErrorHandler.handleError(error, req, res);
    }
  }

  /**
   * Get service statistics (GET /admin/stats)
   */
  static async getStats(req, res) {
    try {
      const stats = {
        cache: cacheService.getStats(),
        langflow: langflowService.getStats(),
        system: {
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          platform: process.platform,
          nodeVersion: process.version,
          pid: process.pid
        },
        timestamp: new Date().toISOString()
      };

      res.json({
        success: true,
        stats: stats
      });
      
    } catch (error) {
      ErrorHandler.handleError(error, req, res);
    }
  }

  /**
   * Health check endpoint (GET /admin/health)
   */
  static async healthCheck(req, res) {
    try {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '2.0.0',
        environment: config.get('NODE_ENV')
      });
      
    } catch (error) {
      res.status(500).json({
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Restart services (POST /admin/restart)
   */
  static async restartServices(req, res) {
    try {
      const { service = 'cache' } = req.body;
      
      logger.info('Service restart requested', {
        service,
        requestedBy: req.ip
      });

      if (service === 'cache') {
        const cleared = cacheService.clear();
        res.json({
          success: true,
          message: `Cache service restarted - ${cleared} entries cleared`,
          service: 'cache',
          timestamp: new Date().toISOString()
        });
      } else {
        res.status(400).json({
          success: false,
          error: 'Only cache service restart is supported',
          availableServices: ['cache']
        });
      }
      
    } catch (error) {
      ErrorHandler.handleError(error, req, res);
    }
  }
}

module.exports = AdminController;
