{"$schema": "https://app.flightcontrol.dev/schema.json", "environments": [{"id": "production", "name": "Production", "region": "us-west-2", "source": {"branch": "main"}, "services": [{"id": "fb-langflow-bridge", "name": "FB Langflow Bridge", "type": "web", "httpPort": 3000, "healthCheckPath": "/health", "cpu": 0.25, "memory": 0.5, "minInstances": 1, "maxInstances": 3, "buildType": "docker", "dockerfilePath": "Dockerfile", "dockerContext": ".", "envVars": {"NODE_ENV": "production", "PORT": "3000", "LOG_LEVEL": "info", "ENABLE_REQUEST_LOGGING": "true", "ENABLE_WEBHOOK_SIGNATURE_VERIFICATION": "true", "RATE_LIMIT_WINDOW": "900000", "RATE_LIMIT_MAX": "100", "RATE_LIMIT_WEBHOOK_MAX": "1000", "MAX_MESSAGE_LENGTH": "2000", "CACHE_TTL": "86400000", "CACHE_MAX_SIZE": "1000"}, "secrets": ["VERIFY_TOKEN", "FB_TOKEN", "LANGFLOW_API_URL", "LANGFLOW_BASE_URL", "WEBHOOK_SECRET", "ADMIN_API_KEY", "JWT_SECRET", "ENCRYPTION_KEY", "BASE_URL", "ALLOWED_ORIGINS"]}]}, {"id": "staging", "name": "Staging", "region": "us-west-2", "source": {"branch": "develop"}, "services": [{"id": "fb-langflow-bridge-staging", "name": "FB Langflow Bridge Staging", "type": "web", "httpPort": 3000, "healthCheckPath": "/health", "cpu": 0.25, "memory": 0.5, "minInstances": 1, "maxInstances": 1, "buildType": "docker", "dockerfilePath": "Dockerfile", "dockerContext": ".", "envVars": {"NODE_ENV": "staging", "PORT": "3000", "LOG_LEVEL": "debug", "DEBUG_MODE": "true", "ENABLE_REQUEST_LOGGING": "true", "ENABLE_WEBHOOK_SIGNATURE_VERIFICATION": "true", "RATE_LIMIT_WINDOW": "900000", "RATE_LIMIT_MAX": "200", "RATE_LIMIT_WEBHOOK_MAX": "2000", "MAX_MESSAGE_LENGTH": "2000", "CACHE_TTL": "3600000", "CACHE_MAX_SIZE": "500"}, "secrets": ["STAGING_VERIFY_TOKEN", "STAGING_FB_TOKEN", "STAGING_LANGFLOW_API_URL", "STAGING_LANGFLOW_BASE_URL", "STAGING_WEBHOOK_SECRET", "STAGING_ADMIN_API_KEY", "STAGING_JWT_SECRET", "STAGING_ENCRYPTION_KEY", "STAGING_BASE_URL", "STAGING_ALLOWED_ORIGINS"]}]}]}