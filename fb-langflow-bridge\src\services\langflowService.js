// src/services/langflowService.js - Langflow integration service
const axios = require('axios');
const config = require('../config');
const logger = require('../utils/logger');
const { ErrorHandler } = require('../utils/errorHandler');

/**
 * Langflow service for handling AI workflow integration
 */
class LangflowService {
  constructor() {
    this.baseURL = config.get('LANGFLOW_BASE_URL');
    this.apiURL = config.get('LANGFLOW_API_URL');
    this.callbackURL = `${config.get('BASE_URL')}/langflow-callback`;
    this.timeout = 30000; // 30 seconds
    this.flowId = this.extractFlowId();
  }

  /**
   * Extract flow ID from API URL
   */
  extractFlowId() {
    try {
      const match = this.apiURL.match(/webhook\/([a-f0-9-]+)/);
      return match ? match[1] : null;
    } catch (error) {
      logger.warn('Could not extract flow ID from API URL', { apiURL: this.apiURL });
      return null;
    }
  }

  /**
   * Trigger Langflow workflow
   */
  async triggerWorkflow(message, sessionId, userProfile = null) {
    try {
      logger.langflow('Triggering workflow', {
        sessionId: sessionId.substring(0, 8) + '...',
        messageLength: message.length,
        hasUserProfile: !!userProfile,
        flowId: this.flowId
      });

      // Mock response in development if enabled
      if (config.get('MOCK_LANGFLOW_API')) {
        return this.getMockResponse(message, sessionId);
      }

      const payload = this.buildPayload(message, sessionId, userProfile);
      
      const response = await axios.post(this.apiURL, payload, {
        headers: { 
          'Content-Type': 'application/json',
          'User-Agent': 'FB-Langflow-Bridge/2.0'
        },
        timeout: this.timeout
      });

      logger.langflow('Workflow triggered successfully', {
        sessionId: sessionId.substring(0, 8) + '...',
        status: response.data.status,
        message: response.data.message
      });

      return this.processWorkflowResponse(response.data, sessionId);

    } catch (error) {
      logger.error('Failed to trigger Langflow workflow', {
        sessionId: sessionId.substring(0, 8) + '...',
        message: message.substring(0, 100),
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });

      ErrorHandler.handleLangflowError(error, 'triggerWorkflow');
    }
  }

  /**
   * Build payload for Langflow webhook
   */
  buildPayload(message, sessionId, userProfile) {
    const payload = {
      message: message,
      session_id: sessionId,
      callback_url: this.callbackURL,
      timestamp: new Date().toISOString(),
      user_info: this.buildUserInfo(sessionId, userProfile)
    };

    // Add additional context if available
    if (config.isDebugMode()) {
      payload.debug = true;
      payload.environment = config.get('NODE_ENV');
    }

    return payload;
  }

  /**
   * Build user info object
   */
  buildUserInfo(sessionId, userProfile) {
    if (!userProfile) {
      return {
        user_id: sessionId,
        first_name: '',
        last_name: '',
        full_name: '',
        gender: '',
        profile_pic: '',
        locale: '',
        timezone: 0
      };
    }

    return {
      user_id: sessionId,
      first_name: userProfile.first_name || '',
      last_name: userProfile.last_name || '',
      full_name: `${userProfile.first_name || ''} ${userProfile.last_name || ''}`.trim(),
      gender: userProfile.gender || '',
      profile_pic: userProfile.profile_pic || '',
      locale: userProfile.locale || 'en_US',
      timezone: userProfile.timezone || 0
    };
  }

  /**
   * Process workflow response
   */
  processWorkflowResponse(data, sessionId) {
    // Handle immediate response
    if (data.result || data.output) {
      logger.langflow('Received immediate response', {
        sessionId: sessionId.substring(0, 8) + '...',
        hasResult: !!data.result,
        hasOutput: !!data.output
      });
      
      return {
        type: 'immediate',
        result: data.result || data.output,
        status: data.status || 'completed'
      };
    }

    // Handle background processing
    if (data.status === 'processing' || data.status === 'queued') {
      logger.langflow('Workflow queued for background processing', {
        sessionId: sessionId.substring(0, 8) + '...',
        status: data.status
      });
      
      return {
        type: 'background',
        message: data.message || 'Processing your request...',
        status: data.status
      };
    }

    // Handle success status
    if (data.status === 'success' || data.message) {
      return {
        type: 'success',
        message: data.message || 'Workflow triggered successfully',
        status: data.status || 'completed'
      };
    }

    // Default response
    return {
      type: 'unknown',
      message: 'Workflow triggered',
      status: 'unknown'
    };
  }

  /**
   * Handle callback from Langflow
   */
  async handleCallback(callbackData) {
    const { session_id, result, status, error } = callbackData;

    if (!session_id) {
      throw new Error('Missing session_id in callback');
    }

    logger.langflow('Received callback', {
      sessionId: session_id.substring(0, 8) + '...',
      status,
      hasResult: !!result,
      hasError: !!error
    });

    if (status === 'completed' && result) {
      return {
        success: true,
        sessionId: session_id,
        result: result,
        action: 'send_message'
      };
    }

    if (status === 'error' || error) {
      logger.error('Langflow callback reported error', {
        sessionId: session_id.substring(0, 8) + '...',
        error: error || 'Unknown error',
        status
      });

      return {
        success: false,
        sessionId: session_id,
        error: error || 'Processing failed',
        action: 'send_error'
      };
    }

    // Handle other statuses
    return {
      success: false,
      sessionId: session_id,
      message: 'Unexpected callback status',
      status: status || 'unknown'
    };
  }

  /**
   * Get mock response for development
   */
  getMockResponse(message, sessionId) {
    const responses = [
      'Xin chào! Tôi đã nhận được tin nhắn của bạn.',
      'Cảm ơn bạn đã liên hệ. Tôi sẽ trả lời ngay.',
      'Đây là phản hồi mô phỏng từ Langflow.',
      'Tin nhắn của bạn đã được xử lý thành công.'
    ];

    const randomResponse = responses[Math.floor(Math.random() * responses.length)];

    logger.info('Mock Langflow response generated', {
      sessionId: sessionId.substring(0, 8) + '...',
      response: randomResponse
    });

    return {
      type: 'immediate',
      result: randomResponse,
      status: 'completed'
    };
  }

  /**
   * Test Langflow connection
   */
  async testConnection(testMessage = 'Test connection', testUserId = 'test-user') {
    try {
      logger.langflow('Testing connection', { testMessage, testUserId });

      const result = await this.triggerWorkflow(testMessage, testUserId, null);
      
      return {
        success: true,
        result: result,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      logger.error('Langflow connection test failed', {
        error: error.message,
        testMessage,
        testUserId
      });

      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Health check for Langflow service
   */
  async healthCheck() {
    try {
      // Try to ping the base URL
      const response = await axios.get(`${this.baseURL}/health`, {
        timeout: 5000
      });

      return {
        status: 'healthy',
        baseURL: this.baseURL,
        flowId: this.flowId,
        response: response.data
      };

    } catch (error) {
      // If health endpoint doesn't exist, try the main URL
      try {
        await axios.get(this.baseURL, { timeout: 5000 });
        return {
          status: 'reachable',
          baseURL: this.baseURL,
          flowId: this.flowId,
          note: 'Base URL reachable but no health endpoint'
        };
      } catch (secondError) {
        return {
          status: 'unhealthy',
          baseURL: this.baseURL,
          flowId: this.flowId,
          error: error.message
        };
      }
    }
  }

  /**
   * Get workflow statistics
   */
  getStats() {
    return {
      baseURL: this.baseURL,
      apiURL: this.apiURL,
      callbackURL: this.callbackURL,
      flowId: this.flowId,
      timeout: this.timeout,
      mockEnabled: config.get('MOCK_LANGFLOW_API')
    };
  }
}

// Export singleton instance
const langflowService = new LangflowService();
module.exports = langflowService;
