// src/services/cacheService.js - Cache management service
const config = require('../config');
const logger = require('../utils/logger');

/**
 * Cache service for managing user data and other cached information
 */
class CacheService {
  constructor() {
    this.cache = new Map();
    this.ttl = config.get('CACHE_TTL');
    this.maxSize = config.get('CACHE_MAX_SIZE');
    this.cleanupInterval = 5 * 60 * 1000; // 5 minutes
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      cleanups: 0
    };

    // Start cleanup timer
    this.startCleanupTimer();
    
    logger.info('Cache service initialized', {
      ttl: this.ttl,
      maxSize: this.maxSize,
      cleanupInterval: this.cleanupInterval
    });
  }

  /**
   * Set cache entry with TTL
   */
  set(key, value, customTTL = null) {
    try {
      const ttl = customTTL || this.ttl;
      const entry = {
        data: value,
        timestamp: Date.now(),
        ttl: ttl,
        expiresAt: Date.now() + ttl,
        accessCount: 0,
        lastAccessed: Date.now()
      };

      // Check cache size limit
      if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
        this.evictOldest();
      }

      this.cache.set(key, entry);
      this.stats.sets++;

      logger.cache('SET', key, {
        size: this.cache.size,
        ttl: ttl,
        expiresAt: new Date(entry.expiresAt).toISOString()
      });

      return true;
    } catch (error) {
      logger.error('Cache set failed', { key, error: error.message });
      return false;
    }
  }

  /**
   * Get cache entry
   */
  get(key) {
    try {
      const entry = this.cache.get(key);

      if (!entry) {
        this.stats.misses++;
        logger.cache('MISS', key);
        return null;
      }

      // Check if expired
      if (Date.now() > entry.expiresAt) {
        this.cache.delete(key);
        this.stats.misses++;
        logger.cache('EXPIRED', key);
        return null;
      }

      // Update access statistics
      entry.accessCount++;
      entry.lastAccessed = Date.now();
      this.stats.hits++;

      logger.cache('HIT', key, {
        accessCount: entry.accessCount,
        age: Date.now() - entry.timestamp
      });

      return entry.data;
    } catch (error) {
      logger.error('Cache get failed', { key, error: error.message });
      return null;
    }
  }

  /**
   * Check if key exists and is not expired
   */
  has(key) {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  /**
   * Delete cache entry
   */
  delete(key) {
    try {
      const deleted = this.cache.delete(key);
      if (deleted) {
        this.stats.deletes++;
        logger.cache('DELETE', key);
      }
      return deleted;
    } catch (error) {
      logger.error('Cache delete failed', { key, error: error.message });
      return false;
    }
  }

  /**
   * Clear all cache entries
   */
  clear() {
    try {
      const count = this.cache.size;
      this.cache.clear();
      
      logger.cache('CLEAR', 'all', { clearedCount: count });
      
      return count;
    } catch (error) {
      logger.error('Cache clear failed', { error: error.message });
      return 0;
    }
  }

  /**
   * Get cache statistics
   */
  getStats() {
    const totalRequests = this.stats.hits + this.stats.misses;
    const hitRate = totalRequests > 0 ? (this.stats.hits / totalRequests * 100).toFixed(2) : 0;

    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: `${hitRate}%`,
      ...this.stats,
      totalRequests,
      memoryUsage: this.getMemoryUsage()
    };
  }

  /**
   * Get memory usage estimation
   */
  getMemoryUsage() {
    let totalSize = 0;
    
    for (const [key, entry] of this.cache) {
      totalSize += this.estimateSize(key) + this.estimateSize(entry);
    }
    
    return {
      estimated: `${(totalSize / 1024).toFixed(2)} KB`,
      entries: this.cache.size
    };
  }

  /**
   * Estimate object size in bytes
   */
  estimateSize(obj) {
    try {
      return JSON.stringify(obj).length * 2; // Rough estimation
    } catch {
      return 100; // Default estimation
    }
  }

  /**
   * Get all cache keys with metadata
   */
  getKeys() {
    const keys = [];
    
    for (const [key, entry] of this.cache) {
      keys.push({
        key: key.length > 20 ? key.substring(0, 20) + '...' : key,
        age: Date.now() - entry.timestamp,
        accessCount: entry.accessCount,
        expiresIn: entry.expiresAt - Date.now(),
        lastAccessed: new Date(entry.lastAccessed).toISOString()
      });
    }
    
    return keys.sort((a, b) => b.accessCount - a.accessCount);
  }

  /**
   * Cleanup expired entries
   */
  cleanup() {
    try {
      const before = this.cache.size;
      const now = Date.now();
      let cleaned = 0;

      for (const [key, entry] of this.cache) {
        if (now > entry.expiresAt) {
          this.cache.delete(key);
          cleaned++;
        }
      }

      if (cleaned > 0) {
        this.stats.cleanups++;
        logger.cache('CLEANUP', 'expired', {
          cleaned,
          before,
          after: this.cache.size
        });
      }

      return cleaned;
    } catch (error) {
      logger.error('Cache cleanup failed', { error: error.message });
      return 0;
    }
  }

  /**
   * Evict oldest entry when cache is full
   */
  evictOldest() {
    try {
      let oldestKey = null;
      let oldestTime = Date.now();

      for (const [key, entry] of this.cache) {
        if (entry.lastAccessed < oldestTime) {
          oldestTime = entry.lastAccessed;
          oldestKey = key;
        }
      }

      if (oldestKey) {
        this.cache.delete(oldestKey);
        logger.cache('EVICT', oldestKey, { reason: 'size_limit' });
      }
    } catch (error) {
      logger.error('Cache eviction failed', { error: error.message });
    }
  }

  /**
   * Start automatic cleanup timer
   */
  startCleanupTimer() {
    setInterval(() => {
      this.cleanup();
    }, this.cleanupInterval);

    logger.debug('Cache cleanup timer started', {
      interval: this.cleanupInterval
    });
  }

  /**
   * User-specific cache methods
   */
  
  /**
   * Set user profile in cache
   */
  setUserProfile(userId, profile) {
    const key = `user_profile:${userId}`;
    return this.set(key, {
      ...profile,
      cached_at: new Date().toISOString()
    });
  }

  /**
   * Get user profile from cache
   */
  getUserProfile(userId) {
    const key = `user_profile:${userId}`;
    return this.get(key);
  }

  /**
   * Set user session data
   */
  setUserSession(userId, sessionData) {
    const key = `user_session:${userId}`;
    const shortTTL = 30 * 60 * 1000; // 30 minutes for sessions
    return this.set(key, sessionData, shortTTL);
  }

  /**
   * Get user session data
   */
  getUserSession(userId) {
    const key = `user_session:${userId}`;
    return this.get(key);
  }

  /**
   * Cache Langflow response
   */
  setLangflowResponse(sessionId, response) {
    const key = `langflow_response:${sessionId}`;
    const shortTTL = 5 * 60 * 1000; // 5 minutes for responses
    return this.set(key, response, shortTTL);
  }

  /**
   * Get cached Langflow response
   */
  getLangflowResponse(sessionId) {
    const key = `langflow_response:${sessionId}`;
    return this.get(key);
  }

  /**
   * Health check
   */
  healthCheck() {
    try {
      const stats = this.getStats();
      const testKey = 'health_check_' + Date.now();
      const testValue = { test: true, timestamp: Date.now() };
      
      // Test set/get operations
      this.set(testKey, testValue, 1000); // 1 second TTL
      const retrieved = this.get(testKey);
      const isWorking = retrieved && retrieved.test === true;
      
      // Cleanup test entry
      this.delete(testKey);
      
      return {
        status: isWorking ? 'healthy' : 'unhealthy',
        stats,
        testPassed: isWorking
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message
      };
    }
  }
}

// Export singleton instance
const cacheService = new CacheService();
module.exports = cacheService;
