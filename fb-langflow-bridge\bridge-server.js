// bridge-server.js - Fixed version to allow static pages access
require('dotenv').config();
const express = require('express');
const axios = require('axios');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const cors = require('cors');
const crypto = require('crypto');
const path = require('path');
const { body, param, validationResult } = require('express-validator');

const app = express();
const port = process.env.PORT || 3000;

// Enhanced Configuration with validation
const config = {
  VERIFY_TOKEN: process.env.VERIFY_TOKEN ? process.env.VERIFY_TOKEN.trim() : '',
  PAGE_ACCESS_TOKEN: process.env.FB_TOKEN ? process.env.FB_TOKEN.trim() : '',
  LANGFLOW_API_URL: process.env.LANGFLOW_API_URL ? process.env.LANGFLOW_API_URL.trim() : 'https://d1r6o0uhss8x1l.cloudfront.net/api/v1/webhook/85f37591-2308-4908-8a8d-22ccf60a8f6a',
  WEBHOOK_SECRET: process.env.WEBHOOK_SECRET ? process.env.WEBHOOK_SECRET.trim() : '',
  ADMIN_API_KEY: process.env.ADMIN_API_KEY ? process.env.ADMIN_API_KEY.trim() : '',
  NODE_ENV: process.env.NODE_ENV ? process.env.NODE_ENV.trim() : 'development',
  ALLOWED_ORIGINS: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',').map(origin => origin.trim()) : ['http://localhost:3000'],
  MAX_MESSAGE_LENGTH: parseInt(process.env.MAX_MESSAGE_LENGTH) || 2000,
  RATE_LIMIT_WINDOW: parseInt(process.env.RATE_LIMIT_WINDOW) || 15 * 60 * 1000,
  RATE_LIMIT_MAX: parseInt(process.env.RATE_LIMIT_MAX) || 100,
  LANGFLOW_BASE_URL: process.env.LANGFLOW_BASE_URL ? process.env.LANGFLOW_BASE_URL.trim() : 'https://d1r6o0uhss8x1l.cloudfront.net',
  BASE_URL: process.env.BASE_URL ? process.env.BASE_URL.trim() : 'http://localhost:3000',
};

// Validate essential configuration
function validateConfig() {  
  // For development, make some fields optional
  if (config.NODE_ENV === 'development') {
    const required = ['VERIFY_TOKEN', 'PAGE_ACCESS_TOKEN'];
    const missing = required.filter(key => !config[key] || config[key].length === 0);
    
    if (missing.length > 0) {
      console.error('❌ Missing required environment variables:', missing.join(', '));
      console.error('💡 For development, you need at least VERIFY_TOKEN and FB_TOKEN');
      process.exit(1);
    }
    
    // Set defaults for development
    if (!config.WEBHOOK_SECRET) config.WEBHOOK_SECRET = 'dev_webhook_secret';
    if (!config.ADMIN_API_KEY) config.ADMIN_API_KEY = 'dev_admin_api_key_with_32_chars_min';
  } else {
    // Production validation
    const required = ['VERIFY_TOKEN', 'PAGE_ACCESS_TOKEN', 'WEBHOOK_SECRET', 'ADMIN_API_KEY'];
    const missing = required.filter(key => !config[key] || config[key].length === 0);
    
    if (missing.length > 0) {
      console.error('❌ Missing required environment variables:', missing.join(', '));
      process.exit(1);
    }
  }
  
  // Validate token formats
  if (config.VERIFY_TOKEN.length < 10) {
    console.error(`❌ VERIFY_TOKEN too short: ${config.VERIFY_TOKEN.length} characters (minimum 10 required)`);
    console.error(`💡 Current value: "${config.VERIFY_TOKEN}"`);
    process.exit(1);
  }
  
  if (config.ADMIN_API_KEY.length < 32) {
    console.error(`❌ ADMIN_API_KEY too short: ${config.ADMIN_API_KEY.length} characters (minimum 32 required)`);
    process.exit(1);
  }
  
  console.log('✅ Configuration validation passed');
}

validateConfig();

// Cache for user data
const userCache = new Map();
const CACHE_EXPIRY = 24 * 60 * 60 * 1000;

app.use('/css', express.static('public'));
app.get('/css/style.css', (req, res) => {
  res.sendFile(path.join(__dirname, 'public/css', 'style.css'));
});
// Security middleware
app.use(helmet({
  contentSecurityPolicy: false, // Disable CSP to avoid conflicts
  crossOriginEmbedderPolicy: false
}));

// CORS configuration
app.use(cors({
  origin: config.ALLOWED_ORIGINS,
  credentials: true
}));

// Rate limiting - Allow higher limits for static files
const generalLimiter = rateLimit({
  windowMs: config.RATE_LIMIT_WINDOW,
  max: config.RATE_LIMIT_MAX,
  message: { error: 'Too many requests, please try again later' },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Bỏ qua toàn bộ webhook subdomain
    const host = req.get('host');
    if (host?.includes('webhook.lailaichat.com')) {
      return true;
    }
    
    // Skip cho các route khác trên domain chính
    return (req.path === '/webhook' && req.method === 'POST') ||
           req.path === '/' ||
           req.path === '/privacy.html' ||
           req.path === '/terms.html' ||
           req.path.startsWith('/css/') ||
           req.path === '/css/style.css' ||
           req.path.match(/\.(css|js|png|jpg|jpeg|gif|ico|svg)$/);
  }
});

app.use(generalLimiter);

// Body parsing with size limits
app.use(express.json({ 
  limit: '10mb',
  verify: (req, res, buf) => {
    req.rawBody = buf;
  }
}));

app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files for specific allowed files only
app.use('/static', express.static('public'));

// Custom static file serving for security
const allowedStaticFiles = ['index.html', 'privacy.html', 'terms.html', 'style.css', 'script.js', 'favicon.ico'];

app.get('/', (req, res) => {
  const filePath = path.join(__dirname, 'public', 'index.html');
  res.sendFile(filePath, (err) => {
    if (err) {
      console.error('Error serving index.html:', err.message);
      res.status(404).json({ error: 'Page not found' });
    }
  });
});

app.get('/privacy.html', (req, res) => {
  const filePath = path.join(__dirname, 'public', 'privacy.html');
  res.sendFile(filePath, (err) => {
    if (err) {
      console.error('Error serving privacy.html:', err.message);
      res.status(404).json({ error: 'Privacy policy not found' });
    }
  });
});

app.get('/terms.html', (req, res) => {
  const filePath = path.join(__dirname, 'public', 'terms.html');
  res.sendFile(filePath, (err) => {
    if (err) {
      console.error('Error serving terms.html:', err.message);
      res.status(404).json({ error: 'Terms of service not found' });
    }
  });
});

// Alternative routes without .html extension
app.get('/privacy', (req, res) => {
  res.redirect('/privacy.html');
});

app.get('/terms', (req, res) => {
  res.redirect('/terms.html');
});

// Logging middleware
app.use((req, res, next) => {
  // Skip logging for static files and webhook echo messages
  if (!req.url.includes('/webhook') || 
      !req.url.match(/\.(css|js|png|jpg|jpeg|gif|ico|svg)$/) ||
      (req.method === 'POST' && req.body?.entry?.[0]?.messaging?.[0]?.message && !req.body.entry[0].messaging[0].message.is_echo)) 
  next();
});

// Admin authentication middleware
function requireAdminAuth(req, res, next) {
  const apiKey = req.headers['x-api-key'] || req.query.api_key;
  
  if (!apiKey || apiKey !== config.ADMIN_API_KEY) {
    return res.status(401).json({ error: 'Unauthorized - Invalid API key' });
  }
  
  next();
}

// Webhook signature verification
function verifyWebhookSignature(req, res, next) {
  if (req.path !== '/webhook' || req.method !== 'POST') {
    return next();
  }
  
  // Skip verification in development mode OR if WEBHOOK_SECRET is the default dev value
  if (config.NODE_ENV === 'development' || config.WEBHOOK_SECRET === 'dev_webhook_secret') {
    return next();
  }
  
  const signature = req.headers['x-hub-signature-256'];
  
  if (!signature) {
    console.warn('🚨 Webhook request without signature');
    return res.status(401).json({ error: 'Missing signature' });
  }
  
  const expectedSignature = 'sha256=' + crypto
    .createHmac('sha256', config.WEBHOOK_SECRET)
    .update(req.rawBody)
    .digest('hex');
  
  if (!crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(expectedSignature))) {
    console.warn('🚨 Invalid webhook signature');
    return res.status(401).json({ error: 'Invalid signature' });
  }
  
  next();
}

// Input validation
const validateMessage = [
  body('message').optional().isLength({ max: config.MAX_MESSAGE_LENGTH }).escape(),
  body('userId').optional().isAlphanumeric().isLength({ min: 1, max: 50 })
];

const validateUserId = [
  param('userId').isAlphanumeric().isLength({ min: 1, max: 50 })
];

function handleValidationErrors(req, res, next) {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Invalid input',
      details: errors.array()
    });
  }
  next();
}

app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '2.0.0'
  });
});

// Webhook verification
app.get('/webhook', (req, res) => {
  const { 'hub.mode': mode, 'hub.verify_token': token, 'hub.challenge': challenge } = req.query;
  
  if (mode === 'subscribe' && token === config.VERIFY_TOKEN) {
    console.log('✅ Webhook verified');
    res.status(200).send(challenge);
  } else {
    console.warn('🚨 Webhook verification failed');
    res.sendStatus(403);
  }
});

// Webhook endpoint
app.post('/webhook', verifyWebhookSignature, async (req, res) => {

  {
    const ts   = new Date().toISOString();
    const ip   = req.ip;
    const head = JSON.stringify(req.headers, null, 2);
    // Giới hạn body in ra 2 000 ký tự để tránh log quá dài
    const body = JSON.stringify(req.body, null, 2).slice(0, 2000);
  }

  const { object, entry } = req.body;

  if (object === 'page') {
    entry.forEach(({ messaging }) => {
      messaging.forEach(event => {
        if (event.message?.text && !event.message.is_echo) {
          if (event.message.text.length > config.MAX_MESSAGE_LENGTH) {
            console.warn(`🚨 Message too long from ${event.sender.id}`);
            return;
          }
          
          handleMessage(event).catch(console.error);
        }
      });
    });
    
    res.status(200).send('EVENT_RECEIVED');
  } else {
    res.sendStatus(404);
  }
});

// Admin endpoints
app.get('/status', requireAdminAuth, (req, res) => {
  res.json({
    status: 'running',
    environment: config.NODE_ENV,
    facebook: config.PAGE_ACCESS_TOKEN ? 'connected' : 'missing token',
    langflow: config.LANGFLOW_API_URL ? 'connected' : 'not configured',
    cache: userCache.size,
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    timestamp: new Date().toISOString()
  });
});

app.get('/test-profile/:userId', requireAdminAuth, validateUserId, handleValidationErrors, async (req, res) => {
  try {
    const profile = await getUserProfile(req.params.userId);
    res.json({ success: true, profile });
  } catch (error) {
    console.error('Profile test error:', error.message);
    res.status(500).json({ success: false, error: 'Profile fetch failed' });
  }
});

app.post('/test-langflow', requireAdminAuth, validateMessage, handleValidationErrors, async (req, res) => {
  const { message = "Xin chào", userId = "test-user" } = req.body;
  
  try {
    let userProfile = null;
    if (userId !== "test-user") {
      userProfile = await getUserProfile(userId);
    }
    
    const response = await callLangflow(message, userId, userProfile);
    res.json({ success: true, input: message, response });
  } catch (error) {
    console.error('Langflow test error:', error.message);
    res.status(500).json({ 
      success: false, 
      error: 'Langflow test failed'
    });
  }
});

// Cache management
app.get('/cache/users', requireAdminAuth, (req, res) => {
  const users = Array.from(userCache.entries()).map(([userId, data]) => ({
    userId: userId.substring(0, 8) + '...',
    name: data.data.first_name,
    cachedAt: new Date(data.timestamp).toISOString()
  }));
  
  res.json({ total: users.length, users });
});

app.post('/cache/clear', requireAdminAuth, (req, res) => {
  const count = userCache.size;
  userCache.clear();
  console.log(`🗑️ Cache cleared: ${count} entries removed`);
  res.json({ message: 'Cache cleared', cleared: count });
});

// Thêm endpoint để nhận callback từ Langflow
app.post('/langflow-callback', async (req, res) => {
  const { session_id, result, status } = req.body;
  
  // Thêm validation
  if (!session_id) {
    return res.status(400).json({ error: 'session_id is required' });
  }
  
  console.log('📞 Received callback from Langflow:');
  
  if (status === 'completed' && result) {
    try {
      // await sendTypingIndicator(session_id, false);
      // await new Promise(resolve => setTimeout(resolve, 20));

      await sendTextMessage(session_id, result);
      console.log('✅ Callback result sent to user:', session_id);
    } catch (error) {
      console.error('❌ Error sending callback result:', error);
      await sendTypingIndicator(session_id, false);
      return res.status(500).json({ error: 'Failed to send message' });
    }
  } else {
    // Tắt typing nếu status không phải completed
    // await sendTypingIndicator(session_id, false);
    // await new Promise(resolve => setTimeout(resolve, 20));
    await sendTextMessage(session_id, "Lỗi hichic");
  }
  
  res.json({ success: true });
});

// Core functions
async function getUserProfile(userId) {
  const cached = userCache.get(userId);
  if (cached && (Date.now() - cached.timestamp) < CACHE_EXPIRY) {
    console.log('📋 Using cached profile:', cached.data);
    return cached.data;
  }

  try {
    const fields = 'first_name,last_name,profile_pic,locale,timezone,gender';
    const url = `https://graph.facebook.com/v19.0/${userId}?fields=${fields}&access_token=${config.PAGE_ACCESS_TOKEN}`;
        
    const { data } = await axios.get(url, { timeout: 10000 });

    // Validate dữ liệu trước khi cache
    if (!data.first_name && !data.last_name) {
      console.warn('⚠️ Facebook returned empty profile data');
    }

    userCache.set(userId, { data, timestamp: Date.now() });
    return data;
    
  } catch (error) {
    console.error(`❌ Profile fetch failed for ${userId}:`, {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message
    });
    
    userCache.set(userId, { 
      data: fallbackData, 
      timestamp: Date.now(),
      isError: true 
    });
    // Return minimal fallback data
    return { 
      first_name: 'User', 
      last_name: '',
      id: userId,
      gender: '',
      profile_pic: ''
    };
  }
}


async function handleMessage(event) {
  const senderId = event.sender.id;
  const messageText = event.message.text;
  const timestamp = new Date().toISOString().slice(11, 19);

  console.log(`\n[${timestamp}] 💬 ${senderId}: "${messageText}"`);

  try {
    await sendTypingIndicator(senderId, true);
    
    const userProfile = await getUserProfile(senderId);
    const userName = userProfile.first_name || 'User';
    
    // Trigger workflow - kết quả sẽ được gửi qua callback
    await callLangflow(messageText, senderId, userProfile);
    
    console.log(`[${timestamp}] 🚀 Langflow webhook triggered for ${userName}`);
    
  } catch (error) {
    console.error(`[${timestamp}] ❌ Error handling message:`, error);
    await sendTypingIndicator(senderId, false);
    await sendTextMessage(senderId, 'Lỗi mất tiêu rồi hii');
  }
}

async function callLangflow(message, userId, userProfile = null) {
  console.log('========== Input to callLangflow (Webhook) ==========', {
    message: message.substring(0, 100),
    userId: userId,
    userProfile: userProfile
  });

  // Kiểm tra userProfile có data không
  if (!userProfile || Object.keys(userProfile).length === 0) {
    console.warn('⚠️ userProfile is empty or null');
  }

  // Flow ID cho webhook
  const FLOW_ID = "85f37591-2308-4908-8a8d-22ccf60a8f6a";
  
  // Construct webhook URL
  const webhookUrl = `${config.LANGFLOW_BASE_URL}/api/v1/webhook/${FLOW_ID}`;

  // Payload cho webhook - gửi trực tiếp data mà workflow cần
  const payload = {
    message: message,
    session_id: userId,
    callback_url: `${config.BASE_URL}/langflow-callback`, // Thêm callback URL
    user_info: userProfile ? {
      user_id: userId,
      first_name: userProfile.first_name || "",
      last_name: userProfile.last_name || "", 
      full_name: `${userProfile.first_name || ""} ${userProfile.last_name || ""}`.trim(),
      gender: userProfile.gender || "",
      profile_pic: userProfile.profile_pic || ""
    } : {
      user_id: userId,
      first_name: "",
      last_name: "",
      full_name: "",
      gender: "",
      profile_pic: ""
    }
  };
  try {
    const { data } = await axios.post(webhookUrl, payload, {
      headers: { "Content-Type": "application/json" },
      timeout: 30000
    });
        
    if (data.message && data.status) {
      console.log(`✅ Webhook triggered successfully: ${data.message} (${data.status})`);
      
      // Nếu workflow trả về kết quả ngay lập tức
      if (data.result || data.output) {
        return data.result || data.output;
      }
      
      // Nếu là background task, có thể cần polling hoặc callback mechanism
      return "Yêu cầu đã được xử lý trong background";
    }
    
    return data.result || data.message || "Webhook đã được kích hoạt thành công";
    
  } catch (error) {
    console.error('❌ Langflow Webhook error:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message,
      url: webhookUrl
    });
    throw new Error('Langflow webhook service unavailable');
  }
}

async function sendTextMessage(recipientId, messageText) {
  try {
    await axios.post(
      `https://graph.facebook.com/v19.0/me/messages?access_token=${config.PAGE_ACCESS_TOKEN}`,
      {
        recipient: { id: recipientId },
        message: { text: messageText }
      },
      { timeout: 10000 }
    );
  } catch (error) {
    console.error('Send message failed:', error.response?.data?.error?.message || error.message);
    throw error;
  }
}

async function sendTypingIndicator(recipientId, isTyping) {
  try {
    console.log(`⌨️ ${isTyping ? 'Showing' : 'Hiding'} typing indicator for ${recipientId}`);
    
    await axios.post(
      `https://graph.facebook.com/v19.0/me/messages?access_token=${config.PAGE_ACCESS_TOKEN}`,
      {
        recipient: { id: recipientId },
        sender_action: isTyping ? "typing_on" : "typing_off"
      },
      { 
        timeout: 5000,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    // Nếu bật typing, tự động tắt sau 18 giây (Facebook auto-timeout là 20s)
    // if (isTyping) {
    //   setTimeout(async () => {
    //     try {
    //       await axios.post(
    //         `https://graph.facebook.com/v19.0/me/messages?access_token=${config.PAGE_ACCESS_TOKEN}`,
    //         {
    //           recipient: { id: recipientId },
    //           sender_action: "typing_off"
    //         },
    //         { timeout: 5000 }
    //       );
    //       console.log(`⏰ Auto-stopped typing indicator for ${recipientId}`);
    //     } catch (error) {
    //       // Silent fail cho auto-cleanup
    //     }
    //   }, 18000); // 18 giây
    // }

  } catch (error) {
    console.error(`❌ Failed to ${isTyping ? 'show' : 'hide'} typing indicator:`, {
      recipientId,
      status: error.response?.status,
      message: error.response?.data?.error?.message || error.message
    });
  }
}

// Error handlers
app.use((err, req, res, next) => {
  console.error('Server error:', err.message);
  
  const errorResponse = config.NODE_ENV === 'production' 
    ? { error: 'Internal server error' }
    : { error: err.message };
    
  res.status(500).json(errorResponse);
});

// 404 handler - Allow specific static files, block everything else
app.use((req, res) => {
  // Don't log 404s for common bot/crawler requests
  if (!req.originalUrl.match(/\.(php|asp|aspx|jsp|cgi)$/i) && 
      !req.originalUrl.includes('wp-') && 
      !req.originalUrl.includes('admin') &&
      !req.originalUrl.includes('robots.txt') &&
      !req.originalUrl.includes('favicon.ico')) {
    console.warn(`🚨 404 - Access denied: ${req.method} ${req.originalUrl} from ${req.ip}`);
  }
  res.status(404).json({ error: 'Page not found' });
});

// Start server
const server = app.listen(port, () => {
  console.log(`
🚀 Secure Bridge Server Started on port ${port}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🛡️  Security: Enhanced
📍 Health: http://localhost:${port}/health
📄 Pages: / /privacy.html /terms.html (Allowed)
🔑 Token: ${config.VERIFY_TOKEN}
📱 Facebook: ${config.PAGE_ACCESS_TOKEN ? '✅ Connected' : '❌ Missing FB_TOKEN'}
🤖 Langflow: ${config.LANGFLOW_API_URL ? '✅ Connected' : '❌ Not configured'}
🌍 Environment: ${config.NODE_ENV}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
  `);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down gracefully...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
  
  setTimeout(() => {
    console.log('⚠️ Forcing shutdown...');
    process.exit(1);
  }, 10000);
});

process.on('SIGTERM', () => {
  console.log('👋 SIGTERM received, shutting down gracefully...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});
