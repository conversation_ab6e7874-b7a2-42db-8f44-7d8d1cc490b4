// src/services/facebookService.js - Facebook API integration service
const axios = require('axios');
const config = require('../config');
const logger = require('../utils/logger');
const { ErrorHandler } = require('../utils/errorHandler');

/**
 * Facebook API service for handling all Facebook interactions
 */
class FacebookService {
  constructor() {
    this.baseURL = 'https://graph.facebook.com/v19.0';
    this.accessToken = config.get('PAGE_ACCESS_TOKEN');
    this.timeout = 10000; // 10 seconds
  }

  /**
   * Get user profile information
   */
  async getUserProfile(userId) {
    try {
      logger.facebook('Getting user profile', { userId: userId.substring(0, 8) + '...' });
      
      // Mock response in development if enabled
      if (config.get('MOCK_FACEBOOK_API')) {
        return this.getMockProfile(userId);
      }

      const fields = 'first_name,last_name,profile_pic,locale,timezone,gender';
      const url = `${this.baseURL}/${userId}`;
      
      const response = await axios.get(url, {
        params: {
          fields,
          access_token: this.accessToken
        },
        timeout: this.timeout
      });

      const profile = response.data;
      
      // Validate profile data
      if (!profile.first_name && !profile.last_name) {
        logger.warn('Facebook returned empty profile data', { userId });
      }

      logger.facebook('Profile retrieved successfully', {
        userId: userId.substring(0, 8) + '...',
        hasName: !!(profile.first_name || profile.last_name),
        hasProfilePic: !!profile.profile_pic
      });

      return profile;
      
    } catch (error) {
      logger.error('Failed to get user profile', {
        userId: userId.substring(0, 8) + '...',
        status: error.response?.status,
        statusText: error.response?.statusText,
        error: error.response?.data?.error
      });

      // Return fallback profile
      return this.getFallbackProfile(userId);
    }
  }

  /**
   * Send text message to user
   */
  async sendTextMessage(recipientId, messageText) {
    try {
      logger.facebook('Sending text message', {
        recipientId: recipientId.substring(0, 8) + '...',
        messageLength: messageText.length
      });

      // Mock response in development if enabled
      if (config.get('MOCK_FACEBOOK_API')) {
        logger.info('Mock: Message sent successfully');
        return { success: true, messageId: 'mock_' + Date.now() };
      }

      const url = `${this.baseURL}/me/messages`;
      const payload = {
        recipient: { id: recipientId },
        message: { text: messageText }
      };

      const response = await axios.post(url, payload, {
        params: { access_token: this.accessToken },
        timeout: this.timeout,
        headers: { 'Content-Type': 'application/json' }
      });

      logger.facebook('Message sent successfully', {
        recipientId: recipientId.substring(0, 8) + '...',
        messageId: response.data.message_id
      });

      return {
        success: true,
        messageId: response.data.message_id,
        recipientId: response.data.recipient_id
      };

    } catch (error) {
      logger.error('Failed to send message', {
        recipientId: recipientId.substring(0, 8) + '...',
        messageText: messageText.substring(0, 100),
        error: error.response?.data?.error
      });

      ErrorHandler.handleFacebookError(error, 'sendTextMessage');
    }
  }

  /**
   * Send typing indicator
   */
  async sendTypingIndicator(recipientId, isTyping = true) {
    try {
      const action = isTyping ? 'typing_on' : 'typing_off';
      
      logger.debug(`${isTyping ? 'Showing' : 'Hiding'} typing indicator`, {
        recipientId: recipientId.substring(0, 8) + '...'
      });

      // Mock response in development if enabled
      if (config.get('MOCK_FACEBOOK_API')) {
        logger.debug('Mock: Typing indicator updated');
        return { success: true };
      }

      const url = `${this.baseURL}/me/messages`;
      const payload = {
        recipient: { id: recipientId },
        sender_action: action
      };

      await axios.post(url, payload, {
        params: { access_token: this.accessToken },
        timeout: 5000, // Shorter timeout for typing indicator
        headers: { 'Content-Type': 'application/json' }
      });

      logger.debug('Typing indicator updated successfully', {
        recipientId: recipientId.substring(0, 8) + '...',
        action
      });

      return { success: true };

    } catch (error) {
      logger.warn('Failed to update typing indicator', {
        recipientId: recipientId.substring(0, 8) + '...',
        isTyping,
        error: error.response?.data?.error?.message || error.message
      });

      // Don't throw error for typing indicator failures
      return { success: false, error: error.message };
    }
  }

  /**
   * Send structured message (buttons, quick replies, etc.)
   */
  async sendStructuredMessage(recipientId, messageData) {
    try {
      logger.facebook('Sending structured message', {
        recipientId: recipientId.substring(0, 8) + '...',
        messageType: messageData.attachment?.type || 'unknown'
      });

      // Mock response in development if enabled
      if (config.get('MOCK_FACEBOOK_API')) {
        logger.info('Mock: Structured message sent successfully');
        return { success: true, messageId: 'mock_structured_' + Date.now() };
      }

      const url = `${this.baseURL}/me/messages`;
      const payload = {
        recipient: { id: recipientId },
        message: messageData
      };

      const response = await axios.post(url, payload, {
        params: { access_token: this.accessToken },
        timeout: this.timeout,
        headers: { 'Content-Type': 'application/json' }
      });

      logger.facebook('Structured message sent successfully', {
        recipientId: recipientId.substring(0, 8) + '...',
        messageId: response.data.message_id
      });

      return {
        success: true,
        messageId: response.data.message_id,
        recipientId: response.data.recipient_id
      };

    } catch (error) {
      logger.error('Failed to send structured message', {
        recipientId: recipientId.substring(0, 8) + '...',
        error: error.response?.data?.error
      });

      ErrorHandler.handleFacebookError(error, 'sendStructuredMessage');
    }
  }

  /**
   * Verify webhook token
   */
  verifyWebhook(mode, token, challenge) {
    logger.facebook('Webhook verification attempt', { mode, token: token?.substring(0, 8) + '...' });

    if (mode === 'subscribe' && token === config.get('VERIFY_TOKEN')) {
      logger.facebook('Webhook verified successfully');
      return challenge;
    } else {
      logger.security('Webhook verification failed', { mode, providedToken: token });
      throw new Error('Webhook verification failed');
    }
  }

  /**
   * Process webhook event
   */
  processWebhookEvent(body) {
    const { object, entry } = body;

    if (object !== 'page') {
      logger.warn('Received non-page webhook event', { object });
      return [];
    }

    const events = [];

    entry.forEach(({ messaging }) => {
      messaging.forEach(event => {
        // Skip echo messages
        if (event.message?.is_echo) {
          return;
        }

        // Process text messages
        if (event.message?.text) {
          const messageLength = event.message.text.length;
          const maxLength = config.get('MAX_MESSAGE_LENGTH');

          if (messageLength > maxLength) {
            logger.warn('Message too long', {
              senderId: event.sender.id,
              messageLength,
              maxLength
            });
            return;
          }

          events.push({
            type: 'message',
            senderId: event.sender.id,
            messageText: event.message.text,
            timestamp: event.timestamp
          });
        }

        // Process postback events
        if (event.postback) {
          events.push({
            type: 'postback',
            senderId: event.sender.id,
            payload: event.postback.payload,
            title: event.postback.title,
            timestamp: event.timestamp
          });
        }
      });
    });

    return events;
  }

  /**
   * Get mock profile for development
   */
  getMockProfile(userId) {
    return {
      id: userId,
      first_name: 'Test',
      last_name: 'User',
      profile_pic: 'https://via.placeholder.com/150',
      locale: 'en_US',
      timezone: 7,
      gender: 'male'
    };
  }

  /**
   * Get fallback profile when API fails
   */
  getFallbackProfile(userId) {
    return {
      id: userId,
      first_name: 'User',
      last_name: '',
      profile_pic: '',
      locale: 'en_US',
      timezone: 0,
      gender: ''
    };
  }

  /**
   * Health check for Facebook API
   */
  async healthCheck() {
    try {
      const url = `${this.baseURL}/me`;
      const response = await axios.get(url, {
        params: { access_token: this.accessToken },
        timeout: 5000
      });

      return {
        status: 'healthy',
        pageId: response.data.id,
        pageName: response.data.name
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.response?.data?.error?.message || error.message
      };
    }
  }
}

// Export singleton instance
const facebookService = new FacebookService();
module.exports = facebookService;
