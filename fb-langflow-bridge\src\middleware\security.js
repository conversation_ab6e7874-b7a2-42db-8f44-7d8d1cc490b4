// src/middleware/security.js - Security middleware collection
const crypto = require('crypto');
const rateLimit = require('express-rate-limit');
const { body, param, validationResult } = require('express-validator');
const config = require('../config');
const logger = require('../utils/logger');
const { 
  AuthenticationError, 
  AuthorizationError, 
  ValidationError,
  RateLimitError,
  ErrorHandler 
} = require('../utils/errorHandler');

/**
 * Security middleware collection
 */
class SecurityMiddleware {
  /**
   * Admin API key authentication
   */
  static requireAdminAuth() {
    return (req, res, next) => {
      const apiKey = req.headers['x-api-key'] || req.query.api_key;
      
      if (!apiKey) {
        logger.security('Missing API key', { ip: req.ip, url: req.originalUrl });
        throw new AuthenticationError('API key required');
      }
      
      if (apiKey !== config.get('ADMIN_API_KEY')) {
        logger.security('Invalid API key', { 
          ip: req.ip, 
          url: req.originalUrl,
          providedKey: apiKey.substring(0, 8) + '...'
        });
        throw new AuthenticationError('Invalid API key');
      }
      
      logger.debug('Admin authentication successful', { ip: req.ip });
      next();
    };
  }

  /**
   * Webhook signature verification
   */
  static verifyWebhookSignature() {
    return (req, res, next) => {
      // Skip verification for non-webhook routes
      if (req.path !== '/webhook' || req.method !== 'POST') {
        return next();
      }
      
      // Skip verification if disabled or in development with default secret
      if (!config.get('ENABLE_WEBHOOK_SIGNATURE_VERIFICATION') ||
          (config.isDevelopment() && config.get('WEBHOOK_SECRET').length === 64)) {
        logger.debug('Webhook signature verification skipped');
        return next();
      }
      
      const signature = req.headers['x-hub-signature-256'];
      
      if (!signature) {
        logger.security('Webhook request without signature', { ip: req.ip });
        throw new AuthenticationError('Missing webhook signature');
      }
      
      const expectedSignature = 'sha256=' + crypto
        .createHmac('sha256', config.get('WEBHOOK_SECRET'))
        .update(req.rawBody)
        .digest('hex');
      
      if (!crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(expectedSignature))) {
        logger.security('Invalid webhook signature', { 
          ip: req.ip,
          providedSignature: signature.substring(0, 16) + '...'
        });
        throw new AuthenticationError('Invalid webhook signature');
      }
      
      logger.debug('Webhook signature verified successfully');
      next();
    };
  }

  /**
   * IP whitelist middleware
   */
  static ipWhitelist() {
    return (req, res, next) => {
      if (!config.get('ENABLE_IP_WHITELIST')) {
        return next();
      }
      
      const trustedIPs = config.get('TRUSTED_IPS');
      if (trustedIPs.length === 0) {
        return next();
      }
      
      const clientIP = req.ip || req.connection.remoteAddress;
      
      if (!trustedIPs.includes(clientIP)) {
        logger.security('IP not in whitelist', { 
          ip: clientIP, 
          url: req.originalUrl 
        });
        throw new AuthorizationError('IP address not allowed');
      }
      
      next();
    };
  }

  /**
   * General rate limiting
   */
  static rateLimiter() {
    return rateLimit({
      windowMs: config.get('RATE_LIMIT_WINDOW'),
      max: config.get('RATE_LIMIT_MAX'),
      message: { 
        success: false,
        error: {
          message: 'Too many requests, please try again later',
          code: 'RATE_LIMIT_EXCEEDED',
          retryAfter: Math.ceil(config.get('RATE_LIMIT_WINDOW') / 1000)
        }
      },
      standardHeaders: true,
      legacyHeaders: false,
      skip: (req) => {
        // Skip rate limiting for certain routes
        const skipPaths = [
          '/health',
          '/webhook',
          '/',
          '/privacy.html',
          '/terms.html'
        ];

        const skipExtensions = /\.(css|js|png|jpg|jpeg|gif|ico|svg)$/;

        return skipPaths.includes(req.path) ||
               skipExtensions.test(req.path) ||
               req.path.startsWith('/css/');
      },
      handler: (req, res) => {
        logger.security('Rate limit exceeded', {
          ip: req.ip,
          url: req.originalUrl,
          userAgent: req.get('User-Agent')
        });

        res.status(429).json({
          success: false,
          error: {
            message: 'Too many requests, please try again later',
            code: 'RATE_LIMIT_EXCEEDED',
            retryAfter: Math.ceil(config.get('RATE_LIMIT_WINDOW') / 1000)
          }
        });
      }
    });
  }

  /**
   * Webhook-specific rate limiting (higher limits)
   */
  static webhookRateLimiter() {
    return rateLimit({
      windowMs: config.get('RATE_LIMIT_WINDOW'),
      max: config.get('RATE_LIMIT_WEBHOOK_MAX'),
      message: { 
        success: false,
        error: {
          message: 'Webhook rate limit exceeded',
          code: 'WEBHOOK_RATE_LIMIT_EXCEEDED'
        }
      },
      skip: (req) => req.path !== '/webhook',
      handler: (req, res) => {
        logger.security('Webhook rate limit exceeded', {
          ip: req.ip,
          headers: req.headers
        });

        res.status(429).json({
          success: false,
          error: {
            message: 'Webhook rate limit exceeded',
            code: 'WEBHOOK_RATE_LIMIT_EXCEEDED'
          }
        });
      }
    });
  }

  /**
   * Input validation middleware
   */
  static validateInput(validations) {
    return [
      ...validations,
      (req, res, next) => {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
          const errorDetails = errors.array().map(error => ({
            field: error.param,
            message: error.msg,
            value: error.value
          }));
          
          logger.warn('Input validation failed', {
            url: req.originalUrl,
            errors: errorDetails
          });
          
          throw new ValidationError('Input validation failed', errorDetails);
        }
        next();
      }
    ];
  }

  /**
   * Message validation rules
   */
  static messageValidation() {
    return [
      body('message')
        .optional()
        .isLength({ max: config.get('MAX_MESSAGE_LENGTH') })
        .withMessage(`Message too long (max ${config.get('MAX_MESSAGE_LENGTH')} characters)`)
        .escape(),
      body('userId')
        .optional()
        .isAlphanumeric()
        .isLength({ min: 1, max: 50 })
        .withMessage('Invalid user ID format')
    ];
  }

  /**
   * User ID validation rules
   */
  static userIdValidation() {
    return [
      param('userId')
        .isAlphanumeric()
        .isLength({ min: 1, max: 50 })
        .withMessage('Invalid user ID format')
    ];
  }

  /**
   * Request logging middleware
   */
  static requestLogger() {
    return (req, res, next) => {
      if (!config.get('ENABLE_REQUEST_LOGGING')) {
        return next();
      }

      const startTime = Date.now();
      
      // Skip logging for static files and health checks
      const skipLogging = req.path === '/health' ||
                         req.path.match(/\.(css|js|png|jpg|jpeg|gif|ico|svg)$/);
      
      if (skipLogging) {
        return next();
      }

      res.on('finish', () => {
        const responseTime = Date.now() - startTime;
        logger.request(req, res, responseTime);
      });

      next();
    };
  }

  /**
   * Request ID middleware
   */
  static requestId() {
    return (req, res, next) => {
      const requestId = crypto.randomBytes(16).toString('hex');
      req.requestId = requestId;
      res.locals.requestId = requestId;
      res.setHeader('X-Request-ID', requestId);
      next();
    };
  }

  /**
   * Security headers middleware
   */
  static securityHeaders() {
    return (req, res, next) => {
      // Remove server header
      res.removeHeader('X-Powered-By');
      
      // Add security headers
      res.setHeader('X-Content-Type-Options', 'nosniff');
      res.setHeader('X-Frame-Options', 'DENY');
      res.setHeader('X-XSS-Protection', '1; mode=block');
      res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
      
      if (config.isProduction()) {
        res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
      }
      
      next();
    };
  }

  /**
   * Body size limit middleware
   */
  static bodySizeLimit() {
    return (req, res, next) => {
      const contentLength = parseInt(req.get('Content-Length') || '0');
      const maxSize = 10 * 1024 * 1024; // 10MB
      
      if (contentLength > maxSize) {
        logger.security('Request body too large', {
          ip: req.ip,
          contentLength,
          maxSize
        });
        throw new ValidationError('Request body too large');
      }
      
      next();
    };
  }
}

module.exports = SecurityMiddleware;
