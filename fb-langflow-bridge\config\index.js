// config/index.js
require('dotenv').config();  // Load .env ở đây

const toInt = (val, fallback) => {
  const n = parseInt(val, 10);
  return Number.isNaN(n) ? fallback : n;
};

module.exports = {
  VERIFY_TOKEN: process.env.VERIFY_TOKEN?.trim() || '',
  PAGE_ACCESS_TOKEN: process.env.FB_TOKEN?.trim() || '',
  LANGFLOW_API_URL: process.env.LANGFLOW_API_URL?.trim()
    || 'http://127.0.0.1:7860/api/v1/run/8096dc20-a7e5-4c25-9f07-acf4a901e857',
  WEBHOOK_SECRET: process.env.WEBHOOK_SECRET?.trim() || '',
  ADMIN_API_KEY: process.env.ADMIN_API_KEY?.trim() || '',
  NODE_ENV: process.env.NODE_ENV?.trim() || 'development',

  // ALLOWED_ORIGINS: "http://a.com,http://b.com"
  ALLOWED_ORIGINS: process.env.ALLOWED_ORIGINS
    ? process.env.ALLOWED_ORIGINS.split(',').map(s => s.trim())
    : ['http://localhost:3000'],

  MAX_MESSAGE_LENGTH: toInt(process.env.MAX_MESSAGE_LENGTH, 2000),
  RATE_LIMIT_WINDOW: toInt(process.env.RATE_LIMIT_WINDOW, 15 * 60 * 1000),
  RATE_LIMIT_MAX: toInt(process.env.RATE_LIMIT_MAX, 100),

  LANGFLOW_BASE_URL: process.env.LANGFLOW_BASE_URL?.trim() || 'http://127.0.0.1:7860',
  BASE_URL: process.env.BASE_URL?.trim() || 'http://localhost:3000',
};
