# Docker ignore file for FB-Langflow Bridge

# Git
.git
.gitignore
.gitattributes

# Node.js
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Development files
tests/
docs/
deployment/
*.md
README.md
CHANGELOG.md
LICENSE

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
tmp/
temp/

# Build artifacts
dist/
build/

# Docker files (don't include in image)
Dockerfile*
.dockerignore
docker-compose*.yml

# CI/CD files
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# Backup files
*.bak
*.backup
*.old

# Local configuration
config/local.js
config/local.json
